use serde::{Deserialize, Serialize};

// Import the new models
pub mod user;
pub mod verification;
pub mod user_settings;
pub mod status;
pub mod contact;

// Re-export the models
pub use user::*;
pub use verification::*;
pub use user_settings::*;
pub use status::*;
pub use contact::*;

// Legacy models for Agora Chat
#[derive(Debug, Serialize, Deserialize)]
pub struct LegacyUser {
    pub id: String,
    pub username: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Message {
    pub id: String,
    pub sender: String,
    pub content: String,
    pub timestamp: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ChatRoom {
    pub id: String,
    pub name: String,
    pub owner: String,
    pub members: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Group {
    pub id: String,
    pub name: String,
    pub owner: String,
    pub members: Vec<String>,
}

// Contact model moved to contact.rs module

#[derive(Debug, Serialize, Deserialize)]
pub struct AgoraLoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AgoraRegisterRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub message: String,
    pub data: Option<T>,
    pub entities: Option<Vec<T>>,
    pub timestamp: Option<u64>,
    pub duration: Option<u64>,
    pub count: Option<u32>,
    pub cursor: Option<String>,
}

# QuiickChat API Documentation

## Overview

QuiickChat is a WhatsApp-like messaging application with a focus on status features. This API provides endpoints for user management, authentication, status posts with 24-hour expiration, and end-to-end encryption key management.

## Getting Started

### Prerequisites

- Rust 1.70+
- MongoDB
- Cloudinary account (for media uploads)
- Twilio account (for SMS verification)

### Environment Setup

1. Copy the `.env` file and update the values:

```bash
# Database Configuration
MONGODB_URI="mongodb://localhost:27017"
DATABASE_NAME="quiickchat"

# Server Configuration
PORT=8080
SERVER_URL="http://localhost:8080"

# JWT Configuration
JWT_SECRET="your_super_secret_jwt_key_change_in_production_min_32_chars"

# Twilio Configuration
TWILIO_ACCOUNT_SID="your_twilio_account_sid"
TWILIO_AUTH_TOKEN="your_twilio_auth_token"
TWILIO_PHONE_NUMBER="your_twilio_phone_number"

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME="your_cloudinary_cloud_name"
CLOUDINARY_API_KEY="your_cloudinary_api_key"
CLOUDINARY_API_SECRET="your_cloudinary_api_secret"
```

### Running the Server

```bash
# Development
cargo run

# Production build
cargo build --release
./target/release/quiickchat
```

## API Documentation

### Base URL
- Local: `http://localhost:8080/api/v1`
- Production: `https://your-domain.com/api/v1`

### Authentication

The API uses JWT (JSON Web Tokens) for authentication. After successful login/verification, you'll receive:
- `access_token`: Short-lived token (15 minutes) for API requests
- `refresh_token`: Long-lived token (7 days) for refreshing access tokens

Include the access token in the Authorization header:
```
Authorization: Bearer <your_access_token>
```

## Postman Collection

### Import Instructions

1. **Import the Collection**:
   - Open Postman
   - Click "Import" button
   - Select `QuiickChat_Postman_Collection.json`
   - The collection will be imported with all endpoints

2. **Configure Variables**:
   - The collection includes pre-configured variables:
     - `baseUrl`: API base URL (default: http://localhost:8080/api/v1)
     - `phone`: Your test phone number (update this)
     - `accessToken`: Auto-populated after login
     - `refreshToken`: Auto-populated after login
     - `userId`: Auto-populated after login

3. **Update Phone Number**:
   - Go to Collection Variables
   - Update the `phone` variable with your test phone number (e.g., "+1234567890")

### Usage Flow

#### 1. Authentication Flow
1. **Register User**: `POST /auth/register`
   - Sends SMS verification code
2. **Verify Registration**: `POST /auth/verify`
   - Verifies SMS code and creates user
3. **Login**: `POST /auth/login`
   - Sends login verification code
4. **Verify Login**: `POST /auth/verify-login`
   - Verifies code and returns JWT tokens
   - Tokens are automatically saved to collection variables

#### 2. Profile Management
1. **Get Profile**: `GET /users/profile`
2. **Update Profile**: `PUT /users/profile`
3. **Upload Profile Picture**: `POST /users/upload-profile-picture`

#### 3. Status Features
1. **Create Text Status**: `POST /status`
2. **Upload Media Status**: `POST /status/upload`
3. **View Status Feed**: `GET /status/feed`
4. **Get My Statuses**: `GET /status/my`
5. **Mark Status as Viewed**: `POST /status/view`
6. **Get Status Views**: `GET /status/{id}/views`

#### 4. Privacy Settings
1. **Get Privacy Settings**: `GET /status/privacy`
2. **Update Privacy Settings**: `PUT /status/privacy`

## Key Features

### WhatsApp-like Status System

- **24-Hour Expiration**: All statuses automatically expire after 24 hours
- **Privacy Controls**: 
  - `Everyone`: All contacts can see
  - `Contacts`: Only contacts can see
  - `ContactsExcept`: All contacts except blocked ones
  - `OnlyShare`: Only specific allowed contacts
- **View Tracking**: See who viewed your status
- **Media Support**: Upload images and videos
- **Contact-based Feed**: Only see statuses from your contacts

### Security Features

- **JWT Authentication**: Secure token-based authentication
- **Phone Verification**: SMS-based user verification
- **End-to-End Encryption Keys**: Store and manage encryption keys
- **Privacy Settings**: Granular control over who can see your content

### Media Handling

- **Cloudinary Integration**: Reliable media storage and delivery
- **Multiple Formats**: Support for images and videos
- **Automatic Optimization**: Cloudinary handles image/video optimization

## API Endpoints Summary

### Authentication
- `POST /auth/register` - Register with phone number
- `POST /auth/verify` - Verify registration code
- `POST /auth/login` - Send login code
- `POST /auth/verify-login` - Verify login and get tokens

### User Management
- `GET /users/profile` - Get current user profile
- `PUT /users/profile` - Update profile
- `POST /users/upload-profile-picture` - Upload profile picture
- `GET /users/details` - Get user by phone
- `DELETE /users/{id}` - Delete user

### Status Management
- `POST /status` - Create text status
- `POST /status/upload` - Upload media status
- `GET /status/feed` - Get contact statuses
- `GET /status/my` - Get my statuses
- `GET /status/user/{id}` - Get user statuses
- `POST /status/view` - Mark status as viewed
- `GET /status/{id}/views` - Get status views
- `DELETE /status/{id}` - Delete status

### Privacy Settings
- `GET /status/privacy` - Get privacy settings
- `PUT /status/privacy` - Update privacy settings

### Key Management
- `POST /keys/identity` - Store identity key
- `GET /keys/identity` - Get identity key
- `POST /keys/signed-pre-key` - Store signed pre-key
- `POST /keys/pre-key` - Store pre-key
- `POST /keys/one-time-keys` - Store one-time keys
- `GET /keys/bundle/{user_id}` - Get key bundle

## Testing Tips

1. **Use Real Phone Numbers**: For SMS verification to work, use real phone numbers
2. **Check Token Expiry**: Access tokens expire in 15 minutes
3. **Status Expiration**: Statuses expire after 24 hours
4. **Media Files**: Use actual image/video files for upload testing
5. **Privacy Testing**: Create multiple users to test privacy settings

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "timestamp": 1640995200000
}
```

Common HTTP status codes:
- `200`: Success
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid/expired token)
- `404`: Not Found
- `500`: Internal Server Error

## Support

For issues or questions:
- Check the server logs for detailed error information
- Ensure all environment variables are properly configured
- Verify MongoDB and external services (Twilio, Cloudinary) are accessible

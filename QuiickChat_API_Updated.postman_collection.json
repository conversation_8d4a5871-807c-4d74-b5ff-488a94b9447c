{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "QuiickChat API - Complete Collection", "description": "Complete API collection for QuiickChat application with Status Management, WebSocket functionality, and Contact Management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string", "description": "Base URL without /api/v1 - this will be added automatically"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "phone", "value": "+2347049670618", "type": "string"}, {"key": "status_id", "value": "", "type": "string"}, {"key": "contact_user_id", "value": "", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.verification_code) {", "        pm.environment.set('verification_code', response.data.verification_code);", "        console.log('Verification code saved:', response.data.verification_code);", "    }", "    if (response.data && response.data.user_id) {", "        pm.environment.set('user_id', response.data.user_id);", "        console.log('User ID saved:', response.data.user_id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\",\n    \"username\": \"testuser\",\n    \"display_name\": \"Test User\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/register", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "register"]}, "description": "Register a new user with phone number verification."}}, {"name": "Verify Phone", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        console.log('Access token saved');", "    }", "    if (response.data && response.data.refresh_token) {", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        console.log('Refresh token saved');", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\",\n    \"verification_code\": \"{{verification_code}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/verify", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "verify"]}, "description": "Verify phone number with SMS code."}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        console.log('Access token saved');", "    }", "    if (response.data && response.data.refresh_token) {", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        console.log('Refresh token saved');", "    }", "    if (response.data && response.data.user && response.data.user.id) {", "        pm.environment.set('user_id', response.data.user.id);", "        console.log('User ID saved:', response.data.user.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/login", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "login"]}, "description": "Login with phone number (sends SMS verification)."}}], "description": "Authentication endpoints for user registration, login, and verification."}, {"name": "📱 Status Management", "item": [{"name": "Create Text Status", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('status_id', response.data.id);", "        console.log('Status ID saved:', response.data.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"content\": \"Hello from Q<PERSON>ickC<PERSON>! 👋 This is a text status update.\",\n    \"content_type\": \"text\",\n    \"privacy\": \"contacts\",\n    \"background_color\": \"#4CAF50\",\n    \"text_color\": \"#FFFFFF\",\n    \"font_style\": \"bold\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/create", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "create"]}, "description": "Create a text-only status update with styling options."}}, {"name": "Upload Image Status", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('status_id', response.data.id);", "        console.log('Image Status ID saved:', response.data.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": [], "description": "Select an image file (JPG, PNG, GIF, WebP)"}, {"key": "status_data", "value": "{\n    \"caption\": \"Check out this amazing photo! 📸\",\n    \"privacy\": \"contacts\",\n    \"background_color\": \"#2196F3\"\n}", "type": "text", "description": "Status metadata as JSON string"}]}, "url": {"raw": "{{base_url}}/api/v1/status/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "upload"]}, "description": "Upload an image status. Backend automatically uploads to Cloudinary and saves the URL."}}, {"name": "Upload Video Status", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('status_id', response.data.id);", "        console.log('Video Status ID saved:', response.data.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": [], "description": "Select a video file (MP4, WebM, MOV, AVI)"}, {"key": "status_data", "value": "{\n    \"caption\": \"Amazing video content! 🎬\",\n    \"privacy\": \"everyone\"\n}", "type": "text", "description": "Status metadata as JSON string"}]}, "url": {"raw": "{{base_url}}/api/v1/status/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "upload"]}, "description": "Upload a video status. Backend automatically uploads to Cloudinary and saves the URL."}}, {"name": "Upload Audio Status", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('status_id', response.data.id);", "        console.log('Audio Status ID saved:', response.data.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": [], "description": "Select an audio file (MP3, WAV, AAC, OGG)"}, {"key": "status_data", "value": "{\n    \"caption\": \"Listen to this! 🎵\",\n    \"privacy\": \"contacts\",\n    \"background_color\": \"#FF5722\"\n}", "type": "text", "description": "Status metadata as JSON string"}]}, "url": {"raw": "{{base_url}}/api/v1/status/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "upload"]}, "description": "Upload an audio status. Backend automatically uploads to Cloudinary and saves the URL."}}, {"name": "Get Status Feed", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/feed", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "feed"]}, "description": "Get status updates from all contacts (status feed)."}}, {"name": "Get My Statuses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/my", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "my"]}, "description": "Get current user's status updates."}}, {"name": "View Status", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status_id\": \"{{status_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/view", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "view"]}, "description": "Mark a status as viewed (for view tracking)."}}, {"name": "Delete Status", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/{{status_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "{{status_id}}"]}, "description": "Delete a status update."}}], "description": "Status management endpoints for creating and managing WhatsApp-like status updates."}, {"name": "🌐 WebSocket Functions", "item": [{"name": "Connect to WebSocket", "request": {"method": "GET", "header": [{"key": "Upgrade", "value": "websocket"}, {"key": "Connection", "value": "Upgrade"}], "url": {"raw": "ws://localhost:8080/api/v1/contacts/ws?token={{access_token}}", "protocol": "ws", "host": ["localhost"], "port": "8080", "path": ["api", "v1", "contacts", "ws"], "query": [{"key": "token", "value": "{{access_token}}", "description": "JWT token for authentication"}]}, "description": "Connect to WebSocket for real-time status updates. JWT token is passed as query parameter for authentication."}}, {"name": "Send Message to User (WebSocket Function)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"function\": \"send_to_user\",\n    \"user_id\": \"{{contact_user_id}}\",\n    \"message\": {\n        \"message_type\": \"custom_message\",\n        \"content\": \"Hello from WebSocket!\",\n        \"timestamp\": 1640995200000\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/websocket/send", "host": ["{{base_url}}"], "path": ["api", "v1", "websocket", "send"]}, "description": "Send a direct message to a specific user via WebSocket. This simulates the send_to_user function."}}, {"name": "Broadcast to Contacts (WebSocket Function)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"function\": \"broadcast_to_contacts\",\n    \"message\": {\n        \"message_type\": \"status_created\",\n        \"user_id\": \"{{user_id}}\",\n        \"status_id\": \"{{status_id}}\",\n        \"content_type\": \"text\",\n        \"content_url\": \"Hello everyone!\",\n        \"created_at_millis\": 1640995200000,\n        \"expires_at_millis\": 1641081600000,\n        \"is_active\": true\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/websocket/broadcast", "host": ["{{base_url}}"], "path": ["api", "v1", "websocket", "broadcast"]}, "description": "Broadcast a status update to all contacts via WebSocket. This simulates the broadcast_to_contacts function."}}, {"name": "Get WebSocket Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/contacts/ws/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "ws", "stats"]}, "description": "Get WebSocket connection statistics and active connections."}}], "description": "WebSocket functionality for real-time communication and status broadcasting."}, {"name": "📞 Contact Management", "item": [{"name": "Upload Contacts", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"contacts\": [\n        {\n            \"display_name\": \"<PERSON>\",\n            \"phone_number\": \"+1234567890\"\n        },\n        {\n            \"display_name\": \"<PERSON>\",\n            \"phone_number\": \"+2347049670619\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/contacts/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "upload"]}, "description": "Upload device contacts to sync with registered users."}}, {"name": "Get Contacts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/contacts", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts"]}, "description": "Get user's synced contacts."}}, {"name": "Sync Contacts", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/contacts/sync", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "sync"]}, "description": "Re-sync contacts to check for newly registered users."}}], "description": "Contact management endpoints for uploading and syncing device contacts."}]}
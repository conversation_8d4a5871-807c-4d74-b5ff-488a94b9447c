use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Verification {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub phone: String,
    pub code: String,
    pub expires_at_millis: i64,
    pub created_at_millis: i64,
    pub attempts: i32,
    pub verified: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TwoFactorAuth {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: String,
    pub secret: String,
    pub backup_codes: Vec<String>,
    pub enabled: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

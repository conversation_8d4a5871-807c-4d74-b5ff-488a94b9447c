import { 
  EC2Client, 
  DescribeInstancesCommand, 
  StartInstancesCommand, 
  StopInstancesCommand,
  Instance,
  InstanceState
} from '@aws-sdk/client-ec2';
import config from '@/config';
import logger from '@/utils/logger';
import { hashInstanceId } from '@/utils/crypto';
import { 
  EC2Instance, 
  RedactedInstanceInfo, 
  InstanceOperationResult,
  ApiError 
} from '@/types';

export class EC2Service {
  private ec2Client: EC2Client;
  private instanceId: string;
  private region: string;

  constructor() {
    this.instanceId = config.aws.instanceId;
    this.region = config.aws.region;
    
    // Initialize EC2 client
    this.ec2Client = new EC2Client({
      region: this.region,
      ...(config.aws.accessKeyId && config.aws.secretAccessKey && {
        credentials: {
          accessKeyId: config.aws.accessKeyId,
          secretAccessKey: config.aws.secretAccessKey,
        },
      }),
    });

    logger.info('EC2Service initialized', { 
      region: this.region, 
      instanceId: `${this.instanceId.substring(0, 10)}...${this.instanceId.slice(-4)}` 
    });
  }

  /**
   * Get current instance information
   */
  async getInstanceInfo(): Promise<EC2Instance> {
    try {
      const command = new DescribeInstancesCommand({
        InstanceIds: [this.instanceId],
      });

      const response = await this.ec2Client.send(command);
      
      if (!response.Reservations || response.Reservations.length === 0) {
        throw this.createError('Instance not found', 404, 'INSTANCE_NOT_FOUND');
      }

      const instance = response.Reservations[0]?.Instances?.[0];
      if (!instance) {
        throw this.createError('Instance data not available', 404, 'INSTANCE_DATA_UNAVAILABLE');
      }

      return this.mapInstanceData(instance);
    } catch (error) {
      logger.error('Failed to get instance info', { error, instanceId: this.instanceId });
      
      if (error instanceof Error && 'name' in error) {
        if (error.name === 'UnauthorizedOperation') {
          throw this.createError('Insufficient permissions to access EC2 instance', 403, 'INSUFFICIENT_PERMISSIONS');
        }
        if (error.name === 'InvalidInstanceID.NotFound') {
          throw this.createError('EC2 instance not found', 404, 'INSTANCE_NOT_FOUND');
        }
      }
      
      throw error;
    }
  }

  /**
   * Get redacted instance information for API responses
   */
  async getRedactedInstanceInfo(includeIps: boolean = false): Promise<RedactedInstanceInfo> {
    const instance = await this.getInstanceInfo();
    
    const redactedInfo: RedactedInstanceInfo = {
      instanceHash: hashInstanceId(this.instanceId),
      region: this.region,
      state: instance.state.name,
      stateCode: instance.state.code,
      hasPublicIp: !!instance.publicIpAddress,
      hasPrivateIp: !!instance.privateIpAddress,
      instanceType: instance.instanceType,
      launchTime: instance.launchTime?.toISOString(),
      availabilityZone: instance.availabilityZone || 'unknown',
      timestamp: new Date().toISOString(),
    };

    // Include IP addresses only if explicitly requested
    if (includeIps) {
      if (instance.publicIpAddress) {
        redactedInfo.publicIp = instance.publicIpAddress;
      }
      if (instance.privateIpAddress) {
        redactedInfo.privateIp = instance.privateIpAddress;
      }
    }

    return redactedInfo;
  }

  /**
   * Start the EC2 instance
   */
  async startInstance(): Promise<InstanceOperationResult> {
    try {
      const currentInstance = await this.getInstanceInfo();
      const currentState = currentInstance.state.name;

      // Check if already running or starting
      if (currentState === 'running') {
        return {
          success: true,
          currentState,
          alreadyInState: true,
          message: 'Instance is already running',
        };
      }

      if (currentState === 'pending') {
        return {
          success: true,
          currentState,
          alreadyInState: true,
          message: 'Instance is already starting',
        };
      }

      // Start the instance
      const command = new StartInstancesCommand({
        InstanceIds: [this.instanceId],
      });

      const response = await this.ec2Client.send(command);
      const newState = response.StartingInstances?.[0]?.CurrentState?.Name || 'unknown';

      logger.info('Instance start initiated', { 
        instanceId: this.instanceId, 
        previousState: currentState, 
        newState 
      });

      return {
        success: true,
        previousState: currentState,
        currentState: newState,
        estimatedTime: '2-3 minutes',
        message: 'Instance start initiated successfully',
      };
    } catch (error) {
      logger.error('Failed to start instance', { error, instanceId: this.instanceId });
      throw this.handleOperationError(error, 'start');
    }
  }

  /**
   * Stop the EC2 instance
   */
  async stopInstance(): Promise<InstanceOperationResult> {
    try {
      const currentInstance = await this.getInstanceInfo();
      const currentState = currentInstance.state.name;

      // Check if already stopped or stopping
      if (currentState === 'stopped') {
        return {
          success: true,
          currentState,
          alreadyInState: true,
          message: 'Instance is already stopped',
        };
      }

      if (currentState === 'stopping') {
        return {
          success: true,
          currentState,
          alreadyInState: true,
          message: 'Instance is already stopping',
        };
      }

      // Stop the instance
      const command = new StopInstancesCommand({
        InstanceIds: [this.instanceId],
      });

      const response = await this.ec2Client.send(command);
      const newState = response.StoppingInstances?.[0]?.CurrentState?.Name || 'unknown';

      logger.info('Instance stop initiated', { 
        instanceId: this.instanceId, 
        previousState: currentState, 
        newState 
      });

      return {
        success: true,
        previousState: currentState,
        currentState: newState,
        estimatedTime: '1-2 minutes',
        message: 'Instance stop initiated successfully',
      };
    } catch (error) {
      logger.error('Failed to stop instance', { error, instanceId: this.instanceId });
      throw this.handleOperationError(error, 'stop');
    }
  }

  /**
   * Wait for instance to reach a specific state
   */
  async waitForState(targetState: string, timeoutMs: number = 300000): Promise<boolean> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      const instance = await this.getInstanceInfo();
      
      if (instance.state.name === targetState) {
        return true;
      }
      
      // Wait 5 seconds before checking again
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    return false;
  }

  /**
   * Map AWS Instance data to our EC2Instance interface
   */
  private mapInstanceData(instance: Instance): EC2Instance {
    return {
      instanceId: instance.InstanceId || '',
      instanceType: instance.InstanceType || 'unknown',
      state: {
        name: instance.State?.Name || 'unknown',
        code: instance.State?.Code || 0,
      },
      publicIpAddress: instance.PublicIpAddress,
      privateIpAddress: instance.PrivateIpAddress,
      launchTime: instance.LaunchTime,
      availabilityZone: instance.Placement?.AvailabilityZone,
      platform: instance.Platform,
      tags: instance.Tags?.reduce((acc, tag) => {
        if (tag.Key && tag.Value) {
          acc[tag.Key] = tag.Value;
        }
        return acc;
      }, {} as Record<string, string>),
    };
  }

  /**
   * Create a standardized API error
   */
  private createError(message: string, statusCode: number, code: string): ApiError {
    const error = new Error(message) as ApiError;
    error.statusCode = statusCode;
    error.code = code;
    return error;
  }

  /**
   * Handle operation errors with appropriate error codes
   */
  private handleOperationError(error: any, operation: string): ApiError {
    if (error instanceof Error && 'name' in error) {
      if (error.name === 'IncorrectInstanceState') {
        return this.createError(
          `Cannot ${operation} instance in current state`, 
          400, 
          'INCORRECT_INSTANCE_STATE'
        );
      }
      if (error.name === 'UnauthorizedOperation') {
        return this.createError(
          `Insufficient permissions to ${operation} instance`, 
          403, 
          'INSUFFICIENT_PERMISSIONS'
        );
      }
    }
    
    return this.createError(
      `Failed to ${operation} instance`, 
      500, 
      'OPERATION_FAILED'
    );
  }
}

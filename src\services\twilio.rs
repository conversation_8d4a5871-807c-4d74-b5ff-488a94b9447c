use std::env;
use log::{info, warn};
use serde::{Deserialize, Serialize};
use crate::errors::AppError;

pub struct TwilioService {
    account_sid: String,
    auth_token: String,
    from_number: String,
    is_enabled: bool,
    client: reqwest::Client,
}

#[derive(Debug, Serialize, Deserialize)]
struct TwilioMessageRequest {
    #[serde(rename = "To")]
    to: String,
    #[serde(rename = "From")]
    from: String,
    #[serde(rename = "Body")]
    body: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct TwilioMessageResponse {
    sid: String,
    status: String,
    error_code: Option<u32>,
    error_message: Option<String>,
}

impl TwilioService {
    pub fn new() -> Self {
        let account_sid = env::var("TWILIO_ACCOUNT_SID").unwrap_or_default();
        let auth_token = env::var("TWILIO_AUTH_TOKEN").unwrap_or_default();
        let from_number = env::var("TWILIO_PHONE_NUMBER").unwrap_or_default();
        let is_enabled = env::var("SMS_ENABLED").unwrap_or_else(|_| "true".to_string()) != "false";
        
        if account_sid.is_empty() || auth_token.is_empty() || from_number.is_empty() {
            warn!("Twilio credentials not fully configured. SMS functionality will be disabled.");
            return Self {
                account_sid,
                auth_token,
                from_number,
                is_enabled: false,
                client: reqwest::Client::new(),
            };
        }
        
        Self {
            account_sid,
            auth_token,
            from_number,
            is_enabled,
            client: reqwest::Client::new(),
        }
    }
    
    // Send a verification code via SMS
    pub async fn send_otp(&self, phone_number: &str, code: &str) -> Result<(), AppError> {
        if !self.is_enabled {
            info!("SMS is disabled. Verification code: {}", code);
            return Ok(());
        }

        if self.account_sid.is_empty() || self.auth_token.is_empty() || self.from_number.is_empty() {
            return Err(AppError::ConfigError("Twilio service not properly configured".to_string()));
        }

        // Format the message
        let message = format!("Your QuiickChat verification code is: {}", code);
        info!("Message to send: {}", message);

        // Send the message
        self.send_message(phone_number, &message).await
    }
    
    // Send a custom message via SMS
    pub async fn send_custom_message(&self, phone_number: &str, message: &str) -> Result<(), AppError> {
        if !self.is_enabled {
            info!("SMS is disabled. Would have sent message to {}: {}", phone_number, message);
            return Ok(());
        }
        
        if self.account_sid.is_empty() || self.auth_token.is_empty() || self.from_number.is_empty() {
            return Err(AppError::ConfigError("Twilio service not properly configured".to_string()));
        }
        
        // Send the message
        self.send_message(phone_number, message).await
    }
    
    // Internal method to send a message via Twilio API
    async fn send_message(&self, to: &str, body: &str) -> Result<(), AppError> {
        // Create the message request
        let message_request = TwilioMessageRequest {
            to: to.to_string(),
            from: self.from_number.clone(),
            body: body.to_string(),
        };

        // Create the URL
        let url = format!(
            "https://api.twilio.com/2010-04-01/Accounts/{}/Messages.json",
            self.account_sid
        );

        // Log minimal information for production
        info!("Sending SMS to {}", to);

        // Send the request
        let response = self.client
            .post(&url)
            .basic_auth(&self.account_sid, Some(&self.auth_token))
            .form(&message_request)
            .send()
            .await
            .map_err(|e| AppError::ExternalServiceError(format!("Failed to send SMS: {}", e)))?;

        // Check if the request was successful
        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(AppError::ExternalServiceError(format!("Twilio API error: {}", error_text)));
        }
        
        // Parse the response
        let message_response: TwilioMessageResponse = response.json().await
            .map_err(|e| AppError::ExternalServiceError(format!("Failed to parse Twilio response: {}", e)))?;
        
        // Check if there was an error in the response
        if let Some(error_message) = message_response.error_message {
            return Err(AppError::ExternalServiceError(format!("Twilio error: {}", error_message)));
        }
        
        info!("SMS sent successfully, SID: {}", message_response.sid);
        
        Ok(())
    }

    // Test Twilio credentials by making a simple API call
    pub async fn test_credentials(&self) -> Result<bool, AppError> {
        if self.account_sid.is_empty() || self.auth_token.is_empty() {
            return Ok(false);
        }

        let url = format!(
            "https://api.twilio.com/2010-04-01/Accounts/{}.json",
            self.account_sid
        );

        // Test Twilio credentials

        let response = self.client
            .get(&url)
            .basic_auth(&self.account_sid, Some(&self.auth_token))
            .send()
            .await
            .map_err(|e| AppError::ExternalServiceError(format!("Failed to test Twilio credentials: {}", e)))?;

        info!("Twilio credentials test response status: {}", response.status());

        if response.status().is_success() {
            info!("Twilio credentials are valid");
            Ok(true)
        } else {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            info!("Twilio credentials test failed: {}", error_text);
            Ok(false)
        }
    }
}

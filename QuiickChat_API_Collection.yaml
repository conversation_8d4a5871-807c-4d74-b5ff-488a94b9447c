openapi: 3.0.0
info:
  title: QuiickChat API
  description: Complete API documentation for QuiickChat - WhatsApp-like messaging app with status feature
  version: 1.0.0
  contact:
    name: QuiickChat API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api/v1
    description: Local development server
  - url: https://api.quiickchat.com/api/v1
    description: Production server

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from login/verify endpoints

  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          description: User ID
        email:
          type: string
          format: email
        username:
          type: string
        phone:
          type: string
        profile_picture:
          type: string
          format: uri
        is_verified:
          type: boolean
        last_seen_millis:
          type: integer
          format: int64
        status:
          type: string
        bio:
          type: string
        address:
          type: string
        created_at_millis:
          type: integer
          format: int64

    Status:
      type: object
      properties:
        id:
          type: string
        user_id:
          type: string
        username:
          type: string
        profile_picture:
          type: string
        content_type:
          type: string
          enum: [Text, Image, Video]
        content_url:
          type: string
        caption:
          type: string
        background_color:
          type: string
        font_style:
          type: string
        created_at_millis:
          type: integer
          format: int64
        expires_at_millis:
          type: integer
          format: int64
        view_count:
          type: integer
        has_viewed:
          type: boolean
        can_view:
          type: boolean

    StatusPrivacy:
      type: string
      enum: [Everyone, Contacts, ContactsExcept, OnlyShare]

    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
        entities:
          type: array
          items:
            type: object
        timestamp:
          type: integer
          format: int64
        count:
          type: integer
        cursor:
          type: string

paths:
  # Authentication Endpoints
  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: Register a new user with phone number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - phone
              properties:
                phone:
                  type: string
                  description: Phone number with country code (e.g., +**********)
                  example: "+**********"
      responses:
        '200':
          description: Registration successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: Invalid phone number or user already exists

  /auth/verify:
    post:
      tags:
        - Authentication
      summary: Verify registration code
      description: Verify the SMS code sent during registration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - phone
                - code
              properties:
                phone:
                  type: string
                  example: "+**********"
                code:
                  type: string
                  example: "123456"
      responses:
        '200':
          description: Verification successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /auth/login:
    post:
      tags:
        - Authentication
      summary: Login with phone number
      description: Send login code to phone number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - phone
              properties:
                phone:
                  type: string
                  example: "+**********"
      responses:
        '200':
          description: Login code sent successfully

  /auth/verify-login:
    post:
      tags:
        - Authentication
      summary: Verify login code
      description: Verify the SMS code sent during login and get JWT tokens
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - phone
                - code
              properties:
                phone:
                  type: string
                  example: "+**********"
                code:
                  type: string
                  example: "123456"
      responses:
        '200':
          description: Login successful with JWT tokens
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          user:
                            $ref: '#/components/schemas/User'
                          auth_tokens:
                            type: object
                            properties:
                              access_token:
                                type: string
                              refresh_token:
                                type: string

  # User Management Endpoints
  /users/profile:
    get:
      tags:
        - Users
      summary: Get current user profile
      security:
        - BearerAuth: []
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'

    put:
      tags:
        - Users
      summary: Update user profile
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                bio:
                  type: string
                address:
                  type: string
                status:
                  type: string
      responses:
        '200':
          description: Profile updated successfully

  /users/upload-profile-picture:
    post:
      tags:
        - Users
      summary: Upload profile picture
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                profile_picture:
                  type: string
                  format: binary
                  description: Image file (JPEG, PNG, etc.)
      responses:
        '200':
          description: Profile picture uploaded successfully

  /users/details:
    get:
      tags:
        - Users
      summary: Get user details by phone
      security:
        - BearerAuth: []
      parameters:
        - name: phone
          in: query
          required: true
          schema:
            type: string
          description: Phone number to lookup
          example: "+**********"
      responses:
        '200':
          description: User details retrieved successfully

  /users/{user_id}:
    delete:
      tags:
        - Users
      summary: Delete user account
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
          description: User ID to delete
      responses:
        '200':
          description: User deleted successfully

  # Status Endpoints
  /status:
    post:
      tags:
        - Status
      summary: Create text status
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - content_type
              properties:
                content_type:
                  $ref: '#/components/schemas/StatusPrivacy'
                caption:
                  type: string
                background_color:
                  type: string
                font_style:
                  type: string
                privacy_setting:
                  $ref: '#/components/schemas/StatusPrivacy'
                allowed_contacts:
                  type: array
                  items:
                    type: string
                blocked_contacts:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: Status created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Status'

  /status/upload:
    post:
      tags:
        - Status
      summary: Upload status with media (image/video)
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - media
                - status_data
              properties:
                media:
                  type: string
                  format: binary
                  description: Image or video file
                status_data:
                  type: string
                  description: JSON string containing status metadata
                  example: '{"content_type":"Image","caption":"My status","privacy_setting":"Contacts"}'
      responses:
        '200':
          description: Status with media created successfully

  /status/feed:
    get:
      tags:
        - Status
      summary: Get contact statuses (status feed)
      security:
        - BearerAuth: []
      parameters:
        - name: contact_ids
          in: query
          schema:
            type: string
          description: Comma-separated list of contact user IDs
          example: "user1,user2,user3"
      responses:
        '200':
          description: Contact statuses retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      entities:
                        type: array
                        items:
                          type: object
                          properties:
                            user_id:
                              type: string
                            username:
                              type: string
                            profile_picture:
                              type: string
                            status_count:
                              type: integer
                            latest_status_time:
                              type: integer
                              format: int64
                            has_unviewed:
                              type: boolean

  /status/my:
    get:
      tags:
        - Status
      summary: Get my statuses
      security:
        - BearerAuth: []
      responses:
        '200':
          description: My statuses retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      entities:
                        type: array
                        items:
                          $ref: '#/components/schemas/Status'

  /status/user/{user_id}:
    get:
      tags:
        - Status
      summary: Get statuses for a specific user
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
          description: User ID whose statuses to retrieve
      responses:
        '200':
          description: User statuses retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      entities:
                        type: array
                        items:
                          $ref: '#/components/schemas/Status'

  /status/view:
    post:
      tags:
        - Status
      summary: Mark status as viewed
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status_id
              properties:
                status_id:
                  type: string
                  description: ID of the status to mark as viewed
      responses:
        '200':
          description: Status viewed successfully

  /status/{status_id}/views:
    get:
      tags:
        - Status
      summary: Get who viewed a specific status
      security:
        - BearerAuth: []
      parameters:
        - name: status_id
          in: path
          required: true
          schema:
            type: string
          description: Status ID to get views for
      responses:
        '200':
          description: Status views retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      entities:
                        type: array
                        items:
                          type: object
                          properties:
                            viewer_id:
                              type: string
                            viewer_username:
                              type: string
                            viewer_profile_picture:
                              type: string
                            viewed_at_millis:
                              type: integer
                              format: int64

  /status/{status_id}:
    delete:
      tags:
        - Status
      summary: Delete a status
      security:
        - BearerAuth: []
      parameters:
        - name: status_id
          in: path
          required: true
          schema:
            type: string
          description: Status ID to delete
      responses:
        '200':
          description: Status deleted successfully

  # Status Privacy Endpoints
  /status/privacy:
    get:
      tags:
        - Status Privacy
      summary: Get privacy settings
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Privacy settings retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          id:
                            type: string
                          user_id:
                            type: string
                          default_privacy:
                            $ref: '#/components/schemas/StatusPrivacy'
                          always_allow:
                            type: array
                            items:
                              type: string
                          always_block:
                            type: array
                            items:
                              type: string
                          read_receipts_enabled:
                            type: boolean
                          created_at_millis:
                            type: integer
                            format: int64
                          updated_at_millis:
                            type: integer
                            format: int64

    put:
      tags:
        - Status Privacy
      summary: Update privacy settings
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                default_privacy:
                  $ref: '#/components/schemas/StatusPrivacy'
                always_allow:
                  type: array
                  items:
                    type: string
                  description: User IDs who can always see status
                always_block:
                  type: array
                  items:
                    type: string
                  description: User IDs who can never see status
                read_receipts_enabled:
                  type: boolean
                  description: Whether to show who viewed status
      responses:
        '200':
          description: Privacy settings updated successfully

  # Key Management Endpoints
  /keys/identity:
    post:
      tags:
        - Keys
      summary: Store identity key
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - identity_key
              properties:
                identity_key:
                  type: string
                  description: Base64 encoded identity key
      responses:
        '200':
          description: Identity key stored successfully

    get:
      tags:
        - Keys
      summary: Get identity key
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Identity key retrieved successfully

  /keys/signed-pre-key:
    post:
      tags:
        - Keys
      summary: Store signed pre-key
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - signed_pre_key
                - signature
              properties:
                signed_pre_key:
                  type: string
                signature:
                  type: string
      responses:
        '200':
          description: Signed pre-key stored successfully

  /keys/pre-key:
    post:
      tags:
        - Keys
      summary: Store pre-key
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - pre_key
              properties:
                pre_key:
                  type: string
      responses:
        '200':
          description: Pre-key stored successfully

  /keys/one-time-keys:
    post:
      tags:
        - Keys
      summary: Store one-time keys
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - one_time_keys
              properties:
                one_time_keys:
                  type: array
                  items:
                    type: string
                  description: Array of base64 encoded one-time keys
      responses:
        '200':
          description: One-time keys stored successfully

  /keys/bundle/{user_id}:
    get:
      tags:
        - Keys
      summary: Get key bundle for user
      security:
        - BearerAuth: []
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
          description: User ID to get key bundle for
      responses:
        '200':
          description: Key bundle retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          identity_key:
                            type: string
                          signed_pre_key:
                            type: string
                          pre_key:
                            type: string
                          one_time_key:
                            type: string

tags:
  - name: Authentication
    description: User registration, login, and verification
  - name: Users
    description: User profile management
  - name: Status
    description: WhatsApp-like status posts with 24-hour expiration
  - name: Status Privacy
    description: Privacy settings for status posts
  - name: Keys
    description: End-to-end encryption key management

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};

use actix::prelude::*;
use actix_web_actors::ws;
use chrono::Utc;
use serde_json::json;
use tokio::sync::RwLock;
use uuid::Uuid;

use crate::models::{StatusUpdateMessage, WebSocketMessage, WebSocketConnection, StatusPrivacy};
use crate::services::ContactService;

// WebSocket actor for individual connections
pub struct WebSocketActor {
    pub user_id: String,
    pub connection_id: String,
    pub websocket_service: Arc<WebSocketService>,
    pub hb: Instant,
}

impl WebSocketActor {
    pub fn new(user_id: String, websocket_service: Arc<WebSocketService>) -> Self {
        let connection_id = Uuid::new_v4().to_string();
        
        Self {
            user_id,
            connection_id,
            websocket_service,
            hb: Instant::now(),
        }
    }

    // Send heartbeat to keep connection alive
    fn hb(&self, ctx: &mut ws::WebsocketContext<Self>) {
        ctx.run_interval(Duration::from_secs(30), |act, ctx| {
            if Instant::now().duration_since(act.hb) > Duration::from_secs(60) {
                // Heartbeat timeout, disconnect
                ctx.stop();
                return;
            }

            ctx.ping(b"");
        });
    }
}

impl Actor for WebSocketActor {
    type Context = ws::WebsocketContext<Self>;

    fn started(&mut self, ctx: &mut Self::Context) {
        // Start heartbeat
        self.hb(ctx);

        // Register connection
        let _connection = WebSocketConnection {
            user_id: self.user_id.clone(),
            connection_id: self.connection_id.clone(),
            connected_at: Utc::now(),
            last_ping: Utc::now(),
        };

        let websocket_service = self.websocket_service.clone();
        let user_id = self.user_id.clone();
        let connection_id = self.connection_id.clone();
        let addr = ctx.address();

        actix::spawn(async move {
            websocket_service.add_connection(user_id, connection_id, addr).await;
        });
    }

    fn stopped(&mut self, _ctx: &mut Self::Context) {
        // Unregister connection
        let websocket_service = self.websocket_service.clone();
        let user_id = self.user_id.clone();
        let connection_id = self.connection_id.clone();

        actix::spawn(async move {
            websocket_service.remove_connection(&user_id, &connection_id).await;
        });
    }
}

impl StreamHandler<Result<ws::Message, ws::ProtocolError>> for WebSocketActor {
    fn handle(&mut self, msg: Result<ws::Message, ws::ProtocolError>, ctx: &mut Self::Context) {
        match msg {
            Ok(ws::Message::Ping(msg)) => {
                self.hb = Instant::now();
                ctx.pong(&msg);
            }
            Ok(ws::Message::Pong(_)) => {
                self.hb = Instant::now();
            }
            Ok(ws::Message::Text(text)) => {
                // Handle incoming text messages (could be used for subscription management)
                if let Ok(message) = serde_json::from_str::<serde_json::Value>(&text) {
                    if let Some(msg_type) = message.get("type").and_then(|v| v.as_str()) {
                        match msg_type {
                            "ping" => {
                                let response = WebSocketMessage {
                                    message_type: "pong".to_string(),
                                    data: json!({}),
                                    timestamp: Utc::now().timestamp_millis(),
                                };
                                ctx.text(serde_json::to_string(&response).unwrap_or_default());
                            }
                            "subscribe_status" => {
                                // Client wants to subscribe to status updates
                                let response = WebSocketMessage {
                                    message_type: "subscribed".to_string(),
                                    data: json!({"status": "subscribed to status updates"}),
                                    timestamp: Utc::now().timestamp_millis(),
                                };
                                ctx.text(serde_json::to_string(&response).unwrap_or_default());
                            }
                            _ => {
                                // Unknown message type
                            }
                        }
                    }
                }
            }
            Ok(ws::Message::Binary(_)) => {
                // Handle binary messages if needed
            }
            Ok(ws::Message::Close(reason)) => {
                ctx.close(reason);
                ctx.stop();
            }
            _ => ctx.stop(),
        }
    }
}

// Implement Handler for TextMessage
impl Handler<TextMessage> for WebSocketActor {
    type Result = ();

    fn handle(&mut self, msg: TextMessage, ctx: &mut Self::Context) {
        ctx.text(msg.0);
    }
}

// Message types for the WebSocket service
#[derive(Message)]
#[rtype(result = "()")]
pub struct SendMessage {
    pub user_id: String,
    pub message: WebSocketMessage,
}

#[derive(Message)]
#[rtype(result = "()")]
pub struct BroadcastToContacts {
    pub user_id: String,
    pub message: StatusUpdateMessage,
}

// Custom message for sending text to WebSocket actor
#[derive(Message)]
#[rtype(result = "()")]
pub struct TextMessage(pub String);

// WebSocket service for managing connections and broadcasting
pub struct WebSocketService {
    // Map of user_id -> Map of connection_id -> WebSocket address
    connections: Arc<RwLock<HashMap<String, HashMap<String, Addr<WebSocketActor>>>>>,
    contact_service: Arc<ContactService>,
}

impl WebSocketService {
    pub fn new(contact_service: Arc<ContactService>) -> Self {
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            contact_service,
        }
    }

    // Add a new WebSocket connection
    pub async fn add_connection(&self, user_id: String, connection_id: String, addr: Addr<WebSocketActor>) {
        let mut connections = self.connections.write().await;
        connections
            .entry(user_id.clone())
            .or_insert_with(HashMap::new)
            .insert(connection_id, addr);
        
        log::info!("WebSocket connection added for user: {}", user_id);
    }

    // Remove a WebSocket connection
    pub async fn remove_connection(&self, user_id: &str, connection_id: &str) {
        let mut connections = self.connections.write().await;
        if let Some(user_connections) = connections.get_mut(user_id) {
            user_connections.remove(connection_id);
            if user_connections.is_empty() {
                connections.remove(user_id);
            }
        }
        
        log::info!("WebSocket connection removed for user: {}", user_id);
    }

    // Send message to a specific user
    pub async fn send_to_user(&self, user_id: &str, message: WebSocketMessage) {
        let connections = self.connections.read().await;
        if let Some(user_connections) = connections.get(user_id) {
            let message_json = serde_json::to_string(&message).unwrap_or_default();
            
            for addr in user_connections.values() {
                addr.do_send(TextMessage(message_json.clone()));
            }
        }
    }

    // Broadcast status update to all contacts of a user (legacy method)
    pub async fn broadcast_status_to_contacts(&self, user_id: &str, status_message: StatusUpdateMessage) {
        // Get all contacts who have this user in their contact list
        match self.contact_service.get_users_with_contact(&format!("user_{}", user_id)).await {
            Ok(contact_user_ids) => {
                let websocket_message = WebSocketMessage {
                    message_type: "status_update".to_string(),
                    data: serde_json::to_value(&status_message).unwrap_or_default(),
                    timestamp: Utc::now().timestamp_millis(),
                };

                // Send to all contacts
                for contact_user_id in contact_user_ids {
                    self.send_to_user(&contact_user_id, websocket_message.clone()).await;
                }

                log::info!("Status update broadcasted for user: {}", user_id);
            }
            Err(e) => {
                log::error!("Failed to get contacts for broadcasting: {}", e);
            }
        }
    }

    // Privacy-aware status broadcasting
    pub async fn broadcast_status_with_privacy(
        &self,
        user_id: &str,
        status_message: StatusUpdateMessage,
        privacy_setting: StatusPrivacy,
        allowed_contacts: &[String],
        blocked_contacts: &[String]
    ) {
        // Get all contacts who have this user in their contact list
        match self.contact_service.get_users_with_contact(&format!("user_{}", user_id)).await {
            Ok(contact_user_ids) => {
                let websocket_message = WebSocketMessage {
                    message_type: "status_update".to_string(),
                    data: serde_json::to_value(&status_message).unwrap_or_default(),
                    timestamp: Utc::now().timestamp_millis(),
                };

                let mut recipients = Vec::new();

                // Apply privacy filtering
                for contact_user_id in contact_user_ids {
                    let should_send = match privacy_setting {
                        StatusPrivacy::Everyone => {
                            // Send to everyone except blocked contacts
                            !blocked_contacts.contains(&contact_user_id)
                        }
                        StatusPrivacy::Contacts => {
                            // Send to contacts except blocked ones
                            !blocked_contacts.contains(&contact_user_id)
                        }
                        StatusPrivacy::ContactsExcept => {
                            // Send to contacts except those in blocked list
                            !blocked_contacts.contains(&contact_user_id)
                        }
                        StatusPrivacy::OnlyShare => {
                            // Only send to specifically allowed contacts
                            allowed_contacts.contains(&contact_user_id) && !blocked_contacts.contains(&contact_user_id)
                        }
                    };

                    if should_send {
                        recipients.push(contact_user_id);
                    }
                }

                // Send to filtered recipients
                let recipient_count = recipients.len();
                for recipient_id in recipients {
                    self.send_to_user(&recipient_id, websocket_message.clone()).await;
                }

                log::info!("Privacy-aware status update broadcasted for user: {} to {} recipients", user_id, recipient_count);
            }
            Err(e) => {
                log::error!("Failed to get contacts for privacy-aware broadcasting: {}", e);
            }
        }
    }

    // Broadcast user online/offline status
    pub async fn broadcast_user_status(&self, user_id: &str, is_online: bool) {
        let status_message = StatusUpdateMessage {
            message_type: if is_online { "user_online".to_string() } else { "user_offline".to_string() },
            user_id: user_id.to_string(),
            status_id: None,
            content_type: None,
            content_url: None,
            caption: None,
            created_at_millis: Some(Utc::now().timestamp_millis()),
            expires_at_millis: None,
            is_active: Some(is_online),
        };

        self.broadcast_status_to_contacts(user_id, status_message).await;
    }

    // Get connection count for a user
    pub async fn get_user_connection_count(&self, user_id: &str) -> usize {
        let connections = self.connections.read().await;
        connections.get(user_id).map_or(0, |user_connections| user_connections.len())
    }

    // Get total connection count
    pub async fn get_total_connections(&self) -> usize {
        let connections = self.connections.read().await;
        connections.values().map(|user_connections| user_connections.len()).sum()
    }
}

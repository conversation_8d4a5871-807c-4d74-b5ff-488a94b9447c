use actix_web::{HttpResponse, ResponseError};
use serde::{Deserialize, Serialize};
use thiserror::Error;

#[derive(Error, Debug)]
pub enum AppError {
    #[error("Authentication error: {0}")]
    AuthError(String),

    #[error("Authentication error: {0}")]
    AuthenticationError(String),

    #[error("Validation error: {0}")]
    ValidationError(String),

    #[error("Not found: {0}")]
    NotFoundError(String),

    #[error("Internal server error: {0}")]
    InternalError(String),

    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Agora API error: {0}")]
    AgoraError(String),

    #[error("Bridge error: {0}")]
    BridgeError(String),

    #[error("External service error: {0}")]
    ExternalServiceError(String),

    #[error("Network error: {0}")]
    NetworkError(String),

    #[error("Deserialization error: {0}")]
    DeserializationError(String),

    #[error("Database error: {0}")]
    DatabaseError(String),
}

#[derive(Serialize, Deserialize)]
pub struct ErrorResponse {
    pub success: bool,
    pub message: String,
}

impl ResponseError for AppError {
    fn error_response(&self) -> HttpResponse {
        let error_response = ErrorResponse {
            success: false,
            message: self.to_string(),
        };

        match self {
            AppError::AuthError(_) | AppError::AuthenticationError(_) => HttpResponse::Unauthorized().json(error_response),
            AppError::ValidationError(_) => HttpResponse::BadRequest().json(error_response),
            AppError::NotFoundError(_) => HttpResponse::NotFound().json(error_response),
            AppError::BridgeError(_) | AppError::AgoraError(_) | AppError::ExternalServiceError(_) | AppError::NetworkError(_) => {
                HttpResponse::BadGateway().json(error_response)
            }
            AppError::ConfigError(_) | AppError::InternalError(_) | AppError::DeserializationError(_) | AppError::DatabaseError(_) => HttpResponse::InternalServerError().json(error_response),
        }
    }
}

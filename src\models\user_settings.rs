use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserSettings {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: String,
    pub notifications: NotificationSettings,
    pub privacy: PrivacySettings,
    pub appearance: AppearanceSettings,
    pub language: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NotificationSettings {
    pub message_notifications: bool,
    pub call_notifications: bool,
    pub group_notifications: bool,
    pub in_app_sounds: bool,
    pub in_app_vibrations: bool,
    pub show_previews: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PrivacySettings {
    pub last_seen: String, // "everyone", "contacts", "nobody"
    pub profile_photo: String, // "everyone", "contacts", "nobody"
    pub status: String, // "everyone", "contacts", "nobody"
    pub read_receipts: bool,
    pub groups: String, // "everyone", "contacts", "nobody"
    pub blocked_contacts: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AppearanceSettings {
    pub theme: String, // "light", "dark", "system"
    pub chat_wallpaper: String,
    pub font_size: String, // "small", "medium", "large"
}

import { Request, Response, NextFunction } from 'express';
import config from '@/config';
import logger from '@/utils/logger';
import { ApiError } from '@/types';

/**
 * API Key authentication middleware
 */
export function authenticateApiKey(req: Request, res: Response, next: NextFunction): void {
  try {
    // Get API key from header or query parameter
    const apiKey = req.headers['x-api-key'] as string || req.query.api_key as string;
    
    if (!apiKey) {
      logger.warn('API request without API key', { 
        ip: req.ip, 
        userAgent: req.get('User-Agent'),
        path: req.path 
      });
      
      const error: ApiError = new Error('API key is required') as ApiError;
      error.statusCode = 401;
      error.code = 'MISSING_API_KEY';
      throw error;
    }
    
    if (apiKey !== config.api.key) {
      logger.warn('API request with invalid API key', { 
        ip: req.ip, 
        userAgent: req.get('User-Agent'),
        path: req.path,
        providedKey: apiKey.substring(0, 4) + '*'.repeat(apiKey.length - 4)
      });
      
      const error: ApiError = new Error('Invalid API key') as ApiError;
      error.statusCode = 401;
      error.code = 'INVALID_API_KEY';
      throw error;
    }
    
    // API key is valid, continue to next middleware
    logger.debug('API key authenticated successfully', { 
      ip: req.ip, 
      path: req.path 
    });
    
    next();
  } catch (error) {
    next(error);
  }
}

/**
 * Optional API key authentication (for endpoints that can work with or without auth)
 */
export function optionalApiKey(req: Request, res: Response, next: NextFunction): void {
  const apiKey = req.headers['x-api-key'] as string || req.query.api_key as string;
  
  if (apiKey) {
    // If API key is provided, validate it
    authenticateApiKey(req, res, next);
  } else {
    // No API key provided, continue without authentication
    next();
  }
}

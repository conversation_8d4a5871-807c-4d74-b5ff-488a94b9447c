use actix_web::{web, App, HttpServer, HttpRequest, HttpResponse};
use actix_web::middleware::{<PERSON><PERSON>, De<PERSON>ultHead<PERSON>};
use actix_cors::Cors;
use std::env;
use std::sync::{Arc, Mutex};
use dotenv::dotenv;
use log::{error, info};
use env_logger::Env;
use crate::utils::mask_sensitive_data;

use crate::middleware::auth::AuthMiddleware;
use crate::middleware::security_headers::SecurityHeaders;
// Temporarily disabled: use crate::middleware::validation::InputValidationMiddleware;
use crate::middleware::rate_limit::{RateLimiter, RateLimitConfig};

mod controllers;
mod routes;
mod models;
mod services;
mod errors;
mod middleware;
mod utils;

use services::{
    MongoDBService, TwilioService,
    UserService, UserSettingsService, KeysService, StatusService
};

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    dotenv().ok();

    // Initialize logger with custom environment
    env_logger::init_from_env(Env::default().default_filter_or("info"));

    // Always bind to 0.0.0.0 to allow external connections
    // This works both locally and in cloud environments like Render
    let host = env::var("HOST").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = env::var("PORT").unwrap_or_else(|_| "8080".to_string());
    let server_url = format!("{}:{}", host, port);

    info!("Server binding to: {}", server_url);

    // Set default JWT secret if not provided
    if env::var("JWT_SECRET").is_err() {
        env::set_var("JWT_SECRET", "default_secret_change_in_production");
    }



    info!("Starting server at: {}", server_url);

    // Initialize MongoDB Service
    let mongodb_service = match MongoDBService::new().await {
        Ok(service) => {
            info!("MongoDB Service initialized successfully");
            Arc::new(service)
        }
        Err(e) => {
            error!("Failed to initialize MongoDB Service: {}", e);
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!("Failed to initialize MongoDB Service: {}", e),
            ));
        }
    };



    // We're now using CloudinaryUploader instead of CloudinaryService
    // No need to initialize CloudinaryService anymore

    // Initialize Twilio Service
    let twilio_service = Arc::new(TwilioService::new());
    info!("Twilio Service initialized successfully");

    // Initialize User Service
    let user_service = Arc::new(UserService::new(mongodb_service.clone()));
    info!("User Service initialized successfully");

    // Initialize User Settings Service
    let user_settings_service = Arc::new(UserSettingsService::new(mongodb_service.clone()));
    info!("User Settings Service initialized successfully");

    // Initialize Keys Service
    let keys_service = Arc::new(KeysService::new(mongodb_service.clone()));
    info!("Keys Service initialized successfully");

    // Initialize Status Service
    let status_service = Arc::new(StatusService::new(mongodb_service.clone()));
    info!("Status Service initialized successfully");

    // Initialize CloudinaryUploader
    let cloudinary_uploader = {
        let cloud_name = env::var("CLOUDINARY_CLOUD_NAME").unwrap_or_else(|_| "default".to_string());
        let api_key = env::var("CLOUDINARY_API_KEY").unwrap_or_else(|_| "".to_string());
        let api_secret = env::var("CLOUDINARY_API_SECRET").unwrap_or_else(|_| "".to_string());

        info!("Initializing CloudinaryUploader with cloud_name: {}, api_key: {}",
            cloud_name,
            mask_sensitive_data(&api_key));
        Arc::new(services::CloudinaryUploader::new(
            cloud_name,
            api_key,
            api_secret,
            "profiles".to_string(),
        ))
    };

    // Create UsersControllerState
    let users_controller_state = web::Data::new(controllers::users_controller::UsersControllerState {
        user_service: user_service.clone(),
        twilio_service: twilio_service.clone(),
        cloudinary_uploader: cloudinary_uploader.clone(),
        user_settings_service: user_settings_service.clone(),
        keys_service: keys_service.clone(),
    });

    // Create KeysControllerState
    let keys_controller_state = web::Data::new(controllers::keys_controller::KeysControllerState {
        keys_service: keys_service.clone(),
    });

    // Create StatusControllerState
    let status_controller_state = web::Data::new(controllers::status_controller::StatusControllerState {
        status_service: status_service.clone(),
        cloudinary_uploader: cloudinary_uploader.clone(),
        user_service: user_service.clone(),
    });

    HttpServer::new(move || {
        let cors = Cors::default()
            .allow_any_origin()
            .allow_any_method()
            .allow_any_header()
            .supports_credentials()
            .max_age(3600);

        // Configure rate limiting
        let rate_limit_config = RateLimitConfig {
            requests_per_minute: 120,  // 120 requests per minute
            burst_size: 30,            // Allow bursts of 30 requests
        };

        App::new()
            // Add middleware in the correct order
            .wrap(Logger::new("%r %s %b %{User-Agent}i %a %T"))
            .wrap(cors)
            // Enable security headers
            .wrap(SecurityHeaders)
            // Enable rate limiting
            .wrap(RateLimiter::new(rate_limit_config))
            // Temporarily disable input validation
            // .wrap(InputValidationMiddleware)
            // Enable authentication
            .wrap(AuthMiddleware)
            // Add default headers
            .wrap(
                DefaultHeaders::new()
                    .add(("X-Content-Type-Options", "nosniff"))
                    .add(("X-XSS-Protection", "1; mode=block"))
            )
            // Configure custom JSON error handlers for common errors
            .app_data(web::JsonConfig::default()
                .error_handler(|err, _| {
                    let response = HttpResponse::BadRequest()
                        .content_type("application/json")
                        .json(middleware::error_handler::JsonErrorResponse {
                            success: false,
                            message: format!("JSON error: {}", err),
                        });
                    actix_web::error::InternalError::from_response(err, response).into()
                }))
            .app_data(web::QueryConfig::default()
                .error_handler(|err, _| {
                    let response = HttpResponse::BadRequest()
                        .content_type("application/json")
                        .json(middleware::error_handler::JsonErrorResponse {
                            success: false,
                            message: format!("Query error: {}", err),
                        });
                    actix_web::error::InternalError::from_response(err, response).into()
                }))
            .app_data(web::PathConfig::default()
                .error_handler(|err, _| {
                    let response = HttpResponse::BadRequest()
                        .content_type("application/json")
                        .json(middleware::error_handler::JsonErrorResponse {
                            success: false,
                            message: format!("Path error: {}", err),
                        });
                    actix_web::error::InternalError::from_response(err, response).into()
                }))
            .app_data(web::FormConfig::default()
                .error_handler(|err, _| {
                    let response = HttpResponse::BadRequest()
                        .content_type("application/json")
                        .json(middleware::error_handler::JsonErrorResponse {
                            success: false,
                            message: format!("Form error: {}", err),
                        });
                    actix_web::error::InternalError::from_response(err, response).into()
                }))
            // Configure multipart form data with 10MB limit for profile picture uploads
            .app_data(actix_multipart::form::MultipartFormConfig::default()
                .total_limit(20 * 1024 * 1024) // Increased to 20MB limit
                .memory_limit(10 * 1024 * 1024) // 10MB in-memory limit
                .error_handler(|err, _| {
                    log::error!("Multipart form error: {}", err);
                    let error_message = if err.to_string().contains("payload") {
                        "File size exceeds the limit".to_string()
                    } else {
                        format!("Multipart form error: {}", err)
                    };

                    let response = HttpResponse::BadRequest()
                        .content_type("application/json")
                        .json(middleware::error_handler::JsonErrorResponse {
                            success: false,
                            message: error_message,
                        });
                    actix_web::error::InternalError::from_response(err, response).into()
                }))
            // Also add the legacy MultipartConfig for backward compatibility
            .app_data(web::PayloadConfig::new(20 * 1024 * 1024)) // 20MB payload limit
            // Add controller states and configure routes
            .app_data(users_controller_state.clone())
            .app_data(keys_controller_state.clone())
            .app_data(status_controller_state.clone())
            .configure(routes::configure_routes)
            // Root endpoint for basic server check
            .route("/", web::get().to(|| async {
                HttpResponse::Ok().json(serde_json::json!({
                    "status": "ok",
                    "service": "quiickchat",
                    "message": "QuiickChat API server is running"
                }))
            }))
            // Default error handler for unhandled errors
            .default_service(web::route().to(|req: HttpRequest| {
                let path = req.path().to_string();
                let method = req.method().to_string();

                async move {
                    HttpResponse::NotFound()
                        .content_type("application/json")
                        .json(middleware::error_handler::JsonErrorResponse {
                            success: false,
                            message: format!("Not found: {} {}", method, path),
                        })
                }
            }))
    })
    .bind(server_url)?
    .run()
    .await
}

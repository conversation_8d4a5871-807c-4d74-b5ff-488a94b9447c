import { Router } from 'express';
import { HealthController } from '@/controllers/HealthController';
import { EC2Controller } from '@/controllers/EC2Controller';
import { authenticateApi<PERSON>ey } from '@/middleware/auth';

const router = Router();
const ec2Controller = new EC2Controller();

// Public routes (no authentication required)
router.get('/health', HealthController.getHealth);

// API v1 routes (authentication required)
const apiV1 = Router();

// EC2 management endpoints
apiV1.get('/status', authenticate<PERSON>pi<PERSON>ey, ec2Controller.getStatus.bind(ec2Controller));
apiV1.post('/start', authenticateApi<PERSON>ey, ec2Controller.startInstance.bind(ec2Controller));
apiV1.post('/stop', authenticateApi<PERSON>ey, ec2Controller.stopInstance.bind(ec2Controller));
apiV1.post('/restart', authenticate<PERSON><PERSON><PERSON><PERSON>, ec2Controller.restartInstance.bind(ec2Controller));
apiV1.get('/wait/:state', authenticate<PERSON><PERSON><PERSON><PERSON>, ec2Controller.waitForState.bind(ec2Controller));

// Mount API v1 routes
router.use('/api/v1', apiV1);

export default router;

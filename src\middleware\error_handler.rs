use actix_web::{
    error::{JsonPayloadError, PathError, QueryPayloadError, UrlencodedError},
    http::{header::ContentType, StatusCode},
    HttpResponse, ResponseError,
};
use serde::Serialize;
use std::fmt;

#[derive(Debug, Serialize, Clone)]
pub struct JsonErrorResponse {
    pub success: bool,
    pub message: String,
}

impl fmt::Display for JsonErrorResponse {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.message)
    }
}

// Custom error handler for actix-web errors
pub struct JsonError {
    pub status_code: StatusCode,
    pub error: JsonErrorResponse,
}

impl fmt::Debug for JsonError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("JsonError")
            .field("status_code", &self.status_code)
            .field("error", &self.error)
            .finish()
    }
}

impl fmt::Display for JsonError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.error)
    }
}

impl ResponseError for JsonError {
    fn status_code(&self) -> StatusCode {
        self.status_code
    }

    fn error_response(&self) -> HttpResponse {
        HttpResponse::build(self.status_code)
            .insert_header(ContentType::json())
            .json(self.error.clone())
    }
}

// Convert actix-web errors to JsonError
impl From<JsonPayloadError> for JsonError {
    fn from(error: JsonPayloadError) -> Self {
        let status_code = error.status_code();
        let error_message = format!("JSON payload error: {}", error);

        JsonError {
            status_code,
            error: JsonErrorResponse {
                success: false,
                message: error_message,
            },
        }
    }
}

impl From<PathError> for JsonError {
    fn from(error: PathError) -> Self {
        let status_code = error.status_code();
        let error_message = format!("Path error: {}", error);

        JsonError {
            status_code,
            error: JsonErrorResponse {
                success: false,
                message: error_message,
            },
        }
    }
}

impl From<QueryPayloadError> for JsonError {
    fn from(error: QueryPayloadError) -> Self {
        let status_code = error.status_code();
        let error_message = format!("Query payload error: {}", error);

        JsonError {
            status_code,
            error: JsonErrorResponse {
                success: false,
                message: error_message,
            },
        }
    }
}

impl From<UrlencodedError> for JsonError {
    fn from(error: UrlencodedError) -> Self {
        let status_code = error.status_code();
        let error_message = format!("Urlencoded error: {}", error);

        JsonError {
            status_code,
            error: JsonErrorResponse {
                success: false,
                message: error_message,
            },
        }
    }
}

// Helper function to create a JSON error response
pub fn json_error(status_code: StatusCode, message: &str) -> HttpResponse {
    HttpResponse::build(status_code)
        .insert_header(ContentType::json())
        .json(JsonErrorResponse {
            success: false,
            message: message.to_string(),
        })
}

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct User {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub email: Option<String>,
    pub username: Option<String>,
    pub phone: String,
    pub profile_picture: Option<String>,
    pub is_verified: bool,
    pub last_seen_millis: i64,
    pub contacts: Vec<String>,
    pub blocked_users: Vec<String>,
    pub status: Option<String>,
    pub created_at_millis: i64,
    pub updated_at_millis: i64,
    pub calls: Vec<CallRecord>,
    pub videos: Vec<VideoRecord>,
    pub chats: Vec<ChatRecord>,
    pub tokens: Vec<TokenRecord>,
    // User profile fields
    pub bio: Option<String>,
    pub address: Option<String>,
    // Encryption related fields
    pub identity_key: Option<String>,
    pub signed_pre_key: Option<String>,
    pub pre_key: Option<String>,
    pub one_time_keys: Vec<String>,
    pub last_key_update: Option<DateTime<Utc>>,
    pub device_id: Option<String>,
    pub registration_id: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CallRecord {
    pub call_id: String,
    pub participants: Vec<String>,
    pub duration: i32,
    pub started_at_millis: i64,
    pub ended_at_millis: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct VideoRecord {
    pub video_id: String,
    pub participants: Vec<String>,
    pub duration: i32,
    pub started_at: DateTime<Utc>,
    pub ended_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChatRecord {
    pub chat_id: String,
    pub sender_id: String,
    pub receiver_id: String,
    pub message: String,
    pub sent_at: DateTime<Utc>,
    pub seen: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TokenRecord {
    pub device_id: String,
    pub token: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EncryptedMessage {
    pub message_id: String,
    pub sender_id: String,
    pub receiver_id: String,
    pub encrypted_data: String,
    pub header: String,
    pub timestamp: DateTime<Utc>,
    pub is_read: bool,
    pub is_forwarded: bool,
    pub forwarded_from: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Session {
    pub user_id: String,
    pub contact_id: String,
    pub root_key: String,
    pub send_chain_key: String,
    pub receive_chain_key: String,
    pub send_chain_length: u32,
    pub receive_chain_length: u32,
    pub last_updated: DateTime<Utc>,
    pub is_active: bool,
}



#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SafeUserResponse {
    pub id: String,
    pub email: Option<String>,
    pub username: Option<String>,
    pub phone: String,
    pub profile_picture: Option<String>,
    pub is_verified: bool,
    pub last_seen_millis: i64,
    pub status: Option<String>,
    pub bio: Option<String>,
    pub address: Option<String>,
    pub created_at_millis: i64,
}

impl From<User> for SafeUserResponse {
    fn from(user: User) -> Self {
        SafeUserResponse {
            id: user.id.map_or_else(|| "".to_string(), |id| id.to_hex()),
            email: user.email,
            username: user.username,
            phone: user.phone,
            profile_picture: user.profile_picture,
            is_verified: user.is_verified,
            last_seen_millis: user.last_seen_millis,
            status: user.status,
            bio: user.bio,
            address: user.address,
            created_at_millis: user.created_at_millis,
        }
    }
}

// Request and response models
#[derive(Debug, Serialize, Deserialize)]
pub struct RegisterRequest {
    pub phone: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VerifyRequest {
    pub phone: String,
    pub code: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProfileRequest {
    pub username: Option<String>,
    pub profile_picture: Option<String>,
    pub status: Option<String>,
    pub bio: Option<String>,
    pub address: Option<String>,
    pub user_id: Option<String>,  // Optional user ID field for fallback
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub phone: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VerifyLoginRequest {
    pub phone: String,
    pub code: String,
}

use actix_web::{web, HttpResponse};
use log::info;

use crate::errors::AppError;
use crate::middleware::Auth;
use crate::models::{ApiResponse, LoginRequest, RegisterRequest};
use crate::services::AgoraService;

pub async fn login(
    login_req: web::Json<LoginRequest>,
    agora_service: web::Data<AgoraService>,
) -> Result<HttpResponse, AppError> {
    info!("Login request for user: {}", login_req.username);

    // Login with Agora Chat SDK
    agora_service.login(&login_req.username, &login_req.password).await?;

    // Generate JWT token with default user role
    let token = Auth::generate_token(&login_req.username, None, vec!["user".to_string()])?;

    let response = ApiResponse {
        success: true,
        message: "Login successful".to_string(),
        data: Some(token),
    };

    Ok(HttpResponse::Ok().json(response))
}

pub async fn register(
    register_req: web::Json<RegisterRequest>,
    agora_service: web::Data<AgoraService>,
) -> Result<HttpResponse, AppError> {
    info!("Register request for user: {}", register_req.username);

    // Register user with Agora Chat SDK
    agora_service.register_user(&register_req.username, &register_req.password).await?;

    let response = ApiResponse::<()> {
        success: true,
        message: "Registration successful".to_string(),
        data: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

pub async fn renew_token(
    token: web::Json<String>,
    agora_service: web::Data<AgoraService>,
) -> Result<HttpResponse, AppError> {
    info!("Token renewal request");

    // Validate the current token
    let claims = Auth::validate_token(&token)?;

    // Renew token with Agora Chat SDK
    agora_service.renew_token(&token).await?;

    // Generate a new JWT token with the same roles
    let new_token = Auth::generate_token(&claims.sub, claims.phone.clone(), claims.roles.clone())?;

    let response = ApiResponse {
        success: true,
        message: "Token renewed successfully".to_string(),
        data: Some(new_token),
    };

    Ok(HttpResponse::Ok().json(response))
}

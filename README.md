# QuiickChat - Real-time Chat Application

A production-ready real-time chat application built with <PERSON><PERSON> backend featuring contact management, status updates, and WebSocket-based real-time communication.

## Project Structure

- `src/` - Rust backend code
  - `controllers/` - API controllers
  - `models/` - Data models
  - `services/` - Business logic
  - `routes/` - API routes
  - `middleware/` - Authentication and other middleware
  - `errors/` - Error handling
  - `bin/` - Binary executables
    - `server.rs` - Server command to run all services
- `js-bridge/` - JavaScript bridge to Agora Chat SDK
- `go_token_generator/` - Go service for Agora token generation
  - `token_service.go` - Main Go file for token generation
  - `go.mod` - Go module definition

## Features

- 📞 **Contact Management**: Upload and sync device contacts
- 📱 **Status Updates**: WhatsApp-like 24-hour status with privacy controls
- 🌐 **Real-time Communication**: WebSocket-based status broadcasting
- 🔐 **JWT Authentication**: Secure user authentication and authorization
- 📷 **Media Upload**: Cloudinary integration for images and videos
- 📧 **SMS Verification**: Twilio integration for phone verification
- 🔒 **Privacy Controls**: Granular status visibility settings

## Prerequisites

- Rust (latest stable)
- MongoDB (local or Atlas)
- Twilio Account (for SMS)
- Cloudinary Account (for media uploads)

## Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd quiickchat
   ```

2. Install JavaScript dependencies:
   ```
   cd js-bridge
   npm install
   cd ..
   ```

3. Set up environment variables:
   ```bash
   cp .env.production.template .env
   # Edit .env with your actual credentials
   ```

   **Required Environment Variables:**
   ```
   JWT_SECRET=your_super_secure_jwt_secret_here_minimum_32_characters
   REFRESH_TOKEN_SECRET=your_super_secure_refresh_token_secret_here
   MONGODB_URI=mongodb://localhost:27017/quiickchat_prod
   TWILIO_ACCOUNT_SID=your_twilio_account_sid
   TWILIO_AUTH_TOKEN=your_twilio_auth_token
   TWILIO_FROM_NUMBER=+**********
   CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
   CLOUDINARY_API_KEY=your_cloudinary_api_key
   CLOUDINARY_API_SECRET=your_cloudinary_api_secret
   ```

## Running the Application

### Using Hot Reload (Recommended for Development)

This project uses `cargo-watch` for hot reloading during development. The configuration is set up in `.cargo/config.toml`.

To start the application with hot reload:

```bash
cargo hot-reload
```

This will:
1. Watch for file changes in your project
2. Automatically rebuild when changes are detected
3. Restart the application with the new changes

You can also use the shorter alias:

```bash
cargo dev
```

### Using the Server Command

The server command will start all required services:

```
cargo run --bin server
```

This will:
1. Start the Go token service
2. Wait for it to initialize
3. Start the JavaScript bridge
4. Wait for it to initialize
5. Start the Rust backend
6. Monitor all processes and restart them if they crash
7. Handle graceful shutdown with Ctrl+C

### Manual Start

If you prefer to start the components separately:

1. Start the Go token service:
   ```
   cd go_token_generator
   go run token_service.go
   ```

2. Start the JavaScript bridge:
   ```
   cd js-bridge
   npm start
   ```

3. In a separate terminal, start the Rust backend:
   ```
   cargo run --bin quiickchat
   ```

## API Endpoints

For a comprehensive list of all API endpoints and their JSON payloads, please see the [API_ROUTES.md](API_ROUTES.md) file.

### Token Generation

The application now uses external services for token generation:

- `GET /api/v1/ac/app-token` - Get an Agora Chat app token
- `POST /api/v1/ac/user-token` - Get an Agora Chat user token

These endpoints return tokens in the following format:

```json
{
    "status": true,
    "msg": "Token generated successfully",
    "data": {
        "agora_token": "007eJxSYHC7PKl3TzPThpNHD8+YZ3GGVZPp1kbNetE18U+eL2Z/VJ+nwJBmbJJiamhhaWycbG5ibpCUZGZgYZiamJpmbpRmlmKaaHNOMEOAj4EhYslDFkYGVgZGBiYGEJ+BARAAAP//u0gdKw=="
    }
}
```

### Authentication

**Note:** Authentication is currently disabled for development purposes. All endpoints are freely accessible.

- `POST /auth/register` - Register a new user
- `POST /auth/login` - Login and get a token
- `POST /auth/renew-token` - Renew an authentication token

### Messaging

- `POST /chat/send` - Send a message

### Chat Rooms

- `POST /chatroom/join` - Join a chat room
- `POST /chatroom/leave` - Leave a chat room
- `GET /chatroom/list` - Get a list of chat rooms

### Contacts

- `POST /contact/add` - Add a contact
- `POST /contact/delete` - Delete a contact
- `GET /contact/list` - Get a list of contacts

### Groups

- `POST /group/create` - Create a group
- `POST /group/join` - Join a group
- `POST /group/leave` - Leave a group



## 🚀 Production Deployment

### Environment Setup

1. **Copy production template:**
   ```bash
   cp .env.production.template .env
   ```

2. **Set secure environment variables:**
   - Use strong, unique JWT secrets (minimum 32 characters)
   - Use production MongoDB connection string
   - Configure Twilio with production credentials
   - Set up Cloudinary for media storage

3. **Security Checklist:**
   - ✅ Strong JWT secrets set
   - ✅ MongoDB connection secured
   - ✅ CORS origins restricted to your domains
   - ✅ SSL/TLS enabled
   - ✅ Environment variables secured
   - ✅ Debug endpoints removed
   - ✅ Logging configured appropriately

### Build and Run

```bash
# Build for production
cargo build --release

# Run the server
./target/release/quiickchat

# Or run with cargo
cargo run --release
```

### Docker Deployment (Optional)

```dockerfile
FROM rust:1.70 as builder
WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/quiickchat /usr/local/bin/quiickchat
EXPOSE 8080
CMD ["quiickchat"]
```

### Monitoring and Logging

- Logs are configured via `RUST_LOG` environment variable
- Use `RUST_LOG=info` for production
- Monitor WebSocket connections via `/api/v1/contacts/ws/stats`
- Set up health checks on the main endpoints

### Scaling Considerations

- **WebSocket Connections**: Use load balancer with sticky sessions
- **Database**: MongoDB with replica sets for high availability
- **Media Storage**: Cloudinary handles CDN and scaling automatically
- **SMS**: Twilio handles delivery and scaling
- **Caching**: Consider Redis for session management at scale

## License

[MIT License](LICENSE)

# QuiickChat - Agora Chat SDK Integration

This project integrates the Agora Chat SDK with a Rust backend using an MVC architecture.

## Project Structure

- `src/` - Rust backend code
  - `controllers/` - API controllers
  - `models/` - Data models
  - `services/` - Business logic
  - `routes/` - API routes
  - `middleware/` - Authentication and other middleware
  - `errors/` - Error handling
  - `bin/` - Binary executables
    - `server.rs` - Server command to run all services
- `js-bridge/` - JavaScript bridge to Agora Chat SDK
- `go_token_generator/` - Go service for Agora token generation
  - `token_service.go` - Main Go file for token generation
  - `go.mod` - Go module definition

## Prerequisites

- Rust (latest stable)
- Go (1.16 or later)
- Node.js (v14 or later)
- npm (v6 or later)
- Agora Chat SDK App Key and Certificate

## Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd quiickchat
   ```

2. Install JavaScript dependencies:
   ```
   cd js-bridge
   npm install
   cd ..
   ```

3. Update the `.env` file with your Agora Chat SDK credentials:
   ```
   AGORA_APP_KEY=your_actual_agora_app_key
   AGORA_APP_ID=your_agora_app_id
   AGORA_APP_CERTIFICATE=your_agora_app_certificate
   JWT_SECRET=your_jwt_secret
   ```

## Running the Application

### Using Hot Reload (Recommended for Development)

This project uses `cargo-watch` for hot reloading during development. The configuration is set up in `.cargo/config.toml`.

To start the application with hot reload:

```bash
cargo hot-reload
```

This will:
1. Watch for file changes in your project
2. Automatically rebuild when changes are detected
3. Restart the application with the new changes

You can also use the shorter alias:

```bash
cargo dev
```

### Using the Server Command

The server command will start all required services:

```
cargo run --bin server
```

This will:
1. Start the Go token service
2. Wait for it to initialize
3. Start the JavaScript bridge
4. Wait for it to initialize
5. Start the Rust backend
6. Monitor all processes and restart them if they crash
7. Handle graceful shutdown with Ctrl+C

### Manual Start

If you prefer to start the components separately:

1. Start the Go token service:
   ```
   cd go_token_generator
   go run token_service.go
   ```

2. Start the JavaScript bridge:
   ```
   cd js-bridge
   npm start
   ```

3. In a separate terminal, start the Rust backend:
   ```
   cargo run --bin quiickchat
   ```

## API Endpoints

For a comprehensive list of all API endpoints and their JSON payloads, please see the [API_ROUTES.md](API_ROUTES.md) file.

### Token Generation

The application now uses external services for token generation:

- `GET /api/v1/ac/app-token` - Get an Agora Chat app token
- `POST /api/v1/ac/user-token` - Get an Agora Chat user token

These endpoints return tokens in the following format:

```json
{
    "status": true,
    "msg": "Token generated successfully",
    "data": {
        "agora_token": "007eJxSYHC7PKl3TzPThpNHD8+YZ3GGVZPp1kbNetE18U+eL2Z/VJ+nwJBmbJJiamhhaWycbG5ibpCUZGZgYZiamJpmbpRmlmKaaHNOMEOAj4EhYslDFkYGVgZGBiYGEJ+BARAAAP//u0gdKw=="
    }
}
```

### Authentication

**Note:** Authentication is currently disabled for development purposes. All endpoints are freely accessible.

- `POST /auth/register` - Register a new user
- `POST /auth/login` - Login and get a token
- `POST /auth/renew-token` - Renew an authentication token

### Messaging

- `POST /chat/send` - Send a message

### Chat Rooms

- `POST /chatroom/join` - Join a chat room
- `POST /chatroom/leave` - Leave a chat room
- `GET /chatroom/list` - Get a list of chat rooms

### Contacts

- `POST /contact/add` - Add a contact
- `POST /contact/delete` - Delete a contact
- `GET /contact/list` - Get a list of contacts

### Groups

- `POST /group/create` - Create a group
- `POST /group/join` - Join a group
- `POST /group/leave` - Leave a group



## Scaling Considerations

- The Agora Chat SDK handles approximately 85-90% of the scaling burden:
  - Message delivery infrastructure
  - Connection management
  - Media processing and storage
  - Push notification infrastructure
  - Global distribution

- The Rust backend handles approximately 10-15% of the scaling burden:
  - API gateway functionality
  - Authentication and authorization
  - Business logic
  - Monitoring and logging
  - Custom features

## License

[MIT License](LICENSE)

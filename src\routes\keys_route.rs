use actix_web::web;
use crate::controllers::keys_controller;

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/keys")
            // Encryption key management routes
            .route("/generate", web::post().to(keys_controller::generate_encryption_keys))
            .route("/rotate", web::post().to(keys_controller::rotate_encryption_keys))
            .route("/revoke", web::post().to(keys_controller::revoke_encryption_keys))
            .route("/{user_id}", web::get().to(keys_controller::get_pre_key_bundle))
            .route("/session/{user_id}", web::get().to(keys_controller::get_session_status))

            // Additional routes for compatibility with previous endpoints
            .route("", web::post().to(keys_controller::generate_encryption_keys))
    );
}

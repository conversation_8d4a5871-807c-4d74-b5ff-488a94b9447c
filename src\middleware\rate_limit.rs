use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    error::<PERSON><PERSON><PERSON>,
    HttpResponse,
    body::BoxBody,
};
use actix_web::body::MessageBody;
use futures::future::{ok, Ready};
use log::warn;
use std::collections::HashMap;
use std::future::Future;
use std::pin::Pin;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};

// Rate limit configuration
#[derive(Clone)]
pub struct RateLimitConfig {
    pub requests_per_minute: u32,
    pub burst_size: u32,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            requests_per_minute: 60,  // Default: 60 requests per minute
            burst_size: 10,           // Default: Allow bursts of 10 requests
        }
    }
}

// Rate limiter state
struct RateLimiterState {
    // Map of IP address to (last request time, request count)
    clients: HashMap<String, (Instant, u32)>,
    config: RateLimitConfig,
}

impl RateLimiterState {
    fn new(config: RateLimitConfig) -> Self {
        Self {
            clients: HashMap::new(),
            config,
        }
    }

    // Check if a request should be rate limited
    fn should_limit(&mut self, client_ip: &str) -> bool {
        let now = Instant::now();
        let entry = self.clients.entry(client_ip.to_string()).or_insert((now, 0));

        // If it's been more than a minute since the last request, reset the counter
        if now.duration_since(entry.0) > Duration::from_secs(60) {
            *entry = (now, 1);
            return false;
        }

        // Increment the counter
        entry.1 += 1;

        // Check if the request count exceeds the limit
        if entry.1 > self.config.requests_per_minute + self.config.burst_size {
            warn!("Rate limit exceeded for IP: {}", client_ip);
            return true;
        }

        false
    }

    // Clean up old entries
    fn cleanup(&mut self) {
        let now = Instant::now();
        self.clients.retain(|_, (time, _)| {
            now.duration_since(*time) < Duration::from_secs(300) // Remove entries older than 5 minutes
        });
    }
}

// Rate limiter middleware
pub struct RateLimiter {
    state: Arc<Mutex<RateLimiterState>>,
}

impl RateLimiter {
    pub fn new(config: RateLimitConfig) -> Self {
        Self {
            state: Arc::new(Mutex::new(RateLimiterState::new(config))),
        }
    }
}

impl<S, B> Transform<S, ServiceRequest> for RateLimiter
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: MessageBody + 'static,
{
    type Response = ServiceResponse<BoxBody>;
    type Error = Error;
    type Transform = RateLimiterMiddleware<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        // Periodically clean up old entries
        let state_clone = self.state.clone();
        actix_web::rt::spawn(async move {
            loop {
                actix_web::rt::time::sleep(Duration::from_secs(60)).await;
                if let Ok(mut state) = state_clone.lock() {
                    state.cleanup();
                }
            }
        });

        ok(RateLimiterMiddleware {
            service,
            state: self.state.clone(),
        })
    }
}

pub struct RateLimiterMiddleware<S> {
    service: S,
    state: Arc<Mutex<RateLimiterState>>,
}

impl<S, B> Service<ServiceRequest> for RateLimiterMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: MessageBody + 'static,
{
    type Response = ServiceResponse<BoxBody>;
    type Error = Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>>>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        // Get the client IP address
        let client_ip = match req.connection_info().realip_remote_addr() {
            Some(ip) => ip.to_string(),
            None => "unknown".to_string(),
        };

        // Check if the request should be rate limited
        let should_limit = {
            let mut state = self.state.lock().unwrap();
            state.should_limit(&client_ip)
        };

        if should_limit {
            // Return a 429 Too Many Requests response in JSON format with Retry-After header
            let response = HttpResponse::TooManyRequests()
                .insert_header(("Retry-After", "60"))
                .json(crate::middleware::error_handler::JsonErrorResponse {
                    success: false,
                    message: "Rate limit error: Too many requests, please try again later".to_string(),
                });

            // Convert the response to a ServiceResponse with BoxBody
            let res = ServiceResponse::new(req.into_parts().0, response.map_into_boxed_body());

            // Return early with the rate limit response
            return Box::pin(async move {
                Ok(res)
            });
        }

        // Continue with the request
        let fut = self.service.call(req);
        Box::pin(async move {
            let res = fut.await?;
            // Map the response body to BoxBody
            Ok(res.map_into_boxed_body())
        })
    }
}

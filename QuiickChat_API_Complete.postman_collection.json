{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "QuiickChat API - Complete Collection", "description": "Complete API collection for QuiickChat with all endpoints including authentication, users, status, contacts, keys, and WebSocket connections.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set base URL if not already set", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'http://localhost:8080');", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "phone_number", "value": "+2347049670618", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.environment.set('user_id', response.data.user_id);", "        console.log('✅ User registered successfully');", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Register a new user with phone number. Sends SMS verification code."}}, {"name": "Verify Registration", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        pm.environment.set('user_id', response.data.user.id);", "        console.log('✅ Registration verified, tokens saved');", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone_number}}\",\n    \"verification_code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify"]}, "description": "Verify registration with SMS code. Returns access and refresh tokens."}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.environment.set('user_id', response.data.user_id);", "        console.log('✅ Login initiated, verification code sent');", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Login existing user. Sends SMS verification code."}}, {"name": "<PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        pm.environment.set('user_id', response.data.user.id);", "        console.log('✅ Login verified, tokens saved');", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone_number}}\",\n    \"verification_code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify-login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify-login"]}, "description": "Verify login with SMS code. Returns access and refresh tokens."}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.environment.set('access_token', response.data.access_token);", "        console.log('✅ Token refreshed successfully');", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh-token", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh-token"]}, "description": "Refresh access token using refresh token."}}, {"name": "Resend Verification Code", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/resend-code", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "resend-code"]}, "description": "Resend SMS verification code."}}, {"name": "Initialize User", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/init", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "init"]}, "description": "Initialize user session and get basic info."}}], "description": "Authentication endpoints for user registration, login, and token management."}, {"name": "👤 Users", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/me", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "me"]}, "description": "Get current user profile information."}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"john_doe\",\n    \"status\": \"Hey there! I'm using QuiickChat 👋\",\n    \"bio\": \"Software developer and tech enthusiast\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Update user profile information."}}, {"name": "Upload Profile Picture", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "profile_picture", "type": "file", "src": [], "description": "Select an image file (JPG, PNG, GIF, WebP)"}]}, "url": {"raw": "{{base_url}}/api/v1/users/upload-profile-picture", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "upload-profile-picture"]}, "description": "Upload profile picture. Automatically uploads to Cloudinary."}}, {"name": "Get Profile Picture", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/profile/picture/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile", "picture", "{{user_id}}"]}, "description": "Get profile picture for a specific user."}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/delete-account", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "delete-account"]}, "description": "Delete user account permanently."}}, {"name": "Get Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/settings", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings"]}, "description": "Get user settings (notifications, privacy, appearance)."}}, {"name": "Update Notification Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"message_notifications\": true,\n    \"group_notifications\": true,\n    \"in_app_sounds\": true,\n    \"in_app_vibrations\": false,\n    \"show_previews\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/notifications", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "notifications"]}, "description": "Update notification settings."}}, {"name": "Update Privacy Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"last_seen\": \"contacts\",\n    \"profile_photo\": \"everyone\",\n    \"status\": \"contacts\",\n    \"read_receipts\": true,\n    \"groups\": \"contacts\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/privacy", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "privacy"]}, "description": "Update privacy settings."}}, {"name": "Update Appearance Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"theme\": \"dark\",\n    \"font_size\": \"medium\",\n    \"chat_wallpaper\": \"default\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/appearance", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "appearance"]}, "description": "Update appearance settings."}}, {"name": "Update Language", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"language\": \"en\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/language", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "language"]}, "description": "Update user language preference."}}, {"name": "Initialize User (Public)", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/init", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "init"]}, "description": "Public endpoint to initialize user session."}}], "description": "User management endpoints for profile, settings, and account operations."}, {"name": "📱 Status", "item": [{"name": "Create Text Status", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"content\": \"Hello from <PERSON><PERSON>ickC<PERSON>! 👋 This is a text status update.\",\n    \"content_type\": \"text\",\n    \"privacy_setting\": \"contacts\",\n    \"background_color\": \"#4CAF50\",\n    \"text_color\": \"#FFFFFF\",\n    \"font_style\": \"bold\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "status"]}, "description": "Create a text-only status update with styling options."}}, {"name": "Upload Image Status", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": [], "description": "Select an image file (JPG, PNG, GIF, WebP)"}, {"key": "status_data", "value": "{\n    \"caption\": \"Check out this amazing photo! 📸\",\n    \"privacy_setting\": \"contacts\",\n    \"background_color\": \"#2196F3\"\n}", "type": "text", "description": "Status metadata as JSON string"}]}, "url": {"raw": "{{base_url}}/api/v1/status/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "upload"]}, "description": "Upload an image status. Backend automatically uploads to Cloudinary and saves the URL."}}, {"name": "Upload Video Status", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": [], "description": "Select a video file (MP4, AVI, MOV, WebM)"}, {"key": "status_data", "value": "{\n    \"caption\": \"Watch this awesome video! 🎬\",\n    \"privacy_setting\": \"contacts\",\n    \"background_color\": \"#9C27B0\"\n}", "type": "text", "description": "Status metadata as JSON string"}]}, "url": {"raw": "{{base_url}}/api/v1/status/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "upload"]}, "description": "Upload a video status. Backend automatically uploads to Cloudinary and saves the URL."}}, {"name": "Upload Audio Status", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": [], "description": "Select an audio file (MP3, WAV, AAC, OGG)"}, {"key": "status_data", "value": "{\n    \"caption\": \"Listen to this! 🎵\",\n    \"privacy_setting\": \"contacts\",\n    \"background_color\": \"#FF5722\"\n}", "type": "text", "description": "Status metadata as JSON string"}]}, "url": {"raw": "{{base_url}}/api/v1/status/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "upload"]}, "description": "Upload an audio status. Backend automatically uploads to Cloudinary and saves the URL."}}, {"name": "Get Status Feed", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/feed", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "feed"]}, "description": "Get status updates from contacts (status feed)."}}, {"name": "Get My Statuses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/my", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "my"]}, "description": "Get current user's status updates."}}, {"name": "Get User Statuses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "user", "{{user_id}}"]}, "description": "Get status updates for a specific user."}}, {"name": "View Status", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status_id\": \"STATUS_ID_HERE\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/view", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "view"]}, "description": "Mark a status as viewed (for read receipts)."}}, {"name": "Get Status Views", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/STATUS_ID_HERE/views", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "STATUS_ID_HERE", "views"]}, "description": "Get who viewed a specific status."}}, {"name": "Delete Status", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/STATUS_ID_HERE", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "STATUS_ID_HERE"]}, "description": "Delete a status update."}}, {"name": "Get Status Privacy Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/privacy", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "privacy"]}, "description": "Get status privacy settings."}}, {"name": "Update Status Privacy Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"default_privacy\": \"contacts\",\n    \"always_allow\": [],\n    \"always_block\": [],\n    \"read_receipts_enabled\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/privacy", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "privacy"]}, "description": "Update status privacy settings."}}], "description": "Status management endpoints for creating, viewing, and managing status updates."}, {"name": "📞 Contacts", "item": [{"name": "Upload Contacts", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"contacts\": [\n        {\n            \"display_name\": \"<PERSON>\",\n            \"phone_number\": \"+1234567890\"\n        },\n        {\n            \"display_name\": \"<PERSON>\",\n            \"phone_number\": \"+0987654321\"\n        },\n        {\n            \"display_name\": \"<PERSON>\",\n            \"phone_number\": \"+1122334455\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/contacts/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "upload"]}, "description": "Upload contacts from device. Merges with existing contacts and checks registration status."}}, {"name": "Get Contacts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/contacts", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts"]}, "description": "Get user's uploaded contacts with registration status."}}, {"name": "Sync Contacts", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/contacts/sync", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "sync"]}, "description": "Re-check registration status for all contacts."}}, {"name": "Connect to WebSocket", "request": {"method": "GET", "header": [{"key": "Upgrade", "value": "websocket"}, {"key": "Connection", "value": "Upgrade"}], "url": {"raw": "ws://localhost:8080/api/v1/contacts/ws?token={{access_token}}", "protocol": "ws", "host": ["localhost"], "port": "8080", "path": ["api", "v1", "contacts", "ws"], "query": [{"key": "token", "value": "{{access_token}}", "description": "JWT token for authentication"}]}, "description": "Connect to WebSocket for real-time status updates. JWT token is passed as query parameter for authentication."}}, {"name": "WebSocket Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/contacts/ws/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "ws", "stats"]}, "description": "Get WebSocket connection statistics."}}], "description": "Contact management and WebSocket endpoints for real-time communication."}, {"name": "🔐 Keys", "item": [{"name": "Generate Encryption Keys", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"key_type\": \"ed25519\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/keys/generate", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "generate"]}, "description": "Generate new encryption keys for secure messaging."}}, {"name": "Rotate Encryption Keys", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/keys/rotate", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "rotate"]}, "description": "Rotate encryption keys for enhanced security."}}, {"name": "Revoke Encryption Keys", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/keys/revoke", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "revoke"]}, "description": "Revoke encryption keys."}}, {"name": "Get Pre-Key Bundle", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/keys/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "{{user_id}}"]}, "description": "Get pre-key bundle for a specific user."}}, {"name": "Get Session Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/keys/session/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "session", "{{user_id}}"]}, "description": "Get session status with a specific user."}}, {"name": "Generate Keys (Alternative)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"key_type\": \"ed25519\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/keys", "host": ["{{base_url}}"], "path": ["api", "v1", "keys"]}, "description": "Alternative endpoint to generate encryption keys."}}], "description": "Encryption key management for secure messaging."}, {"name": "🏥 Health & Monitoring", "item": [{"name": "Health Check", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Health check endpoint for monitoring and deployment platforms."}}, {"name": "Root Endpoint", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Root endpoint for basic server check."}}], "description": "Health check and monitoring endpoints."}]}
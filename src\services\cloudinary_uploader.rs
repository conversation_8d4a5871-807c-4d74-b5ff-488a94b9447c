use crate::errors::AppError;
use log::info;
use reqwest::multipart::{Form, Part};
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::fs::File;
use tokio::io::AsyncReadExt;
use mime_guess::from_path;
use std::collections::HashMap;
use sha1::{Digest, Sha1};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CloudinaryConfig {
    pub cloud_name: String,
    pub api_key: String,
    pub api_secret: String,
    pub default_folder: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UploadResponse {
    pub public_id: String,
    pub version: u64,
    pub signature: String,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub format: String,
    pub resource_type: String,
    pub created_at: String,
    pub tags: Option<Vec<String>>,
    pub bytes: u64,
    pub url: String,
    pub secure_url: String,
}

#[derive(Clone)]
pub struct CloudinaryUploader {
    config: CloudinaryConfig,
    client: reqwest::Client,
}

impl CloudinaryUploader {
    pub fn new(cloud_name: String, api_key: String, api_secret: String, default_folder: String) -> Self {
        let config = CloudinaryConfig {
            cloud_name,
            api_key,
            api_secret,
            default_folder,
        };

        CloudinaryUploader {
            config,
            client: reqwest::Client::new(),
        }
    }

    // Check if the Cloudinary credentials are valid
    pub async fn check_credentials(&self) -> Result<bool, AppError> {
        // If any of the required credentials are missing, return false
        if self.config.cloud_name.is_empty() || self.config.api_key.is_empty() || self.config.api_secret.is_empty() {
            info!("Cloudinary credentials are incomplete");
            return Ok(false);
        }

        // Try to ping the Cloudinary API
        let url = format!(
            "https://api.cloudinary.com/v1_1/{}/ping",
            self.config.cloud_name
        );

        info!("Checking Cloudinary credentials by pinging: {}", url);

        let response = self.client
            .get(&url)
            .header("Authorization", format!("Basic {}", base64::Engine::encode(&base64::engine::general_purpose::STANDARD, format!("{}:{}", self.config.api_key, self.config.api_secret))))
            .send()
            .await;

        match response {
            Ok(res) => {
                if res.status().is_success() {
                    info!("Cloudinary credentials are valid");
                    Ok(true)
                } else {
                    info!("Cloudinary credentials are invalid. Status: {}", res.status());
                    Ok(false)
                }
            },
            Err(e) => {
                info!("Failed to check Cloudinary credentials: {}", e);
                Ok(false)
            }
        }
    }

    pub async fn upload_file_from_path(
        &self,
        file_path: &Path,
        folder: Option<&str>,
        transformation: Option<&str>,
        public_id: Option<&str>,
    ) -> Result<UploadResponse, AppError> {
        let _timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| AppError::InternalError(format!("Failed to get system time: {}", e)))?
            .as_secs()
            .to_string();

        let mut file = File::open(file_path).await
            .map_err(|e| AppError::InternalError(format!("Failed to open file: {}", e)))?;
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).await
            .map_err(|e| AppError::InternalError(format!("Failed to read file: {}", e)))?;

        let file_name = file_path
            .file_name()
            .and_then(|n| n.to_str())
            .ok_or_else(|| AppError::ValidationError("Invalid file name".to_string()))?;

        self.upload_bytes(
            &buffer,
            file_name,
            folder,
            transformation,
            public_id,
            Some(file_path),
        ).await
    }

    pub async fn upload_bytes(
        &self,
        buffer: &[u8],
        file_name: &str,
        folder: Option<&str>,
        transformation: Option<&str>,
        public_id: Option<&str>,
        file_path: Option<&Path>,
    ) -> Result<UploadResponse, AppError> {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| AppError::InternalError(format!("Failed to get system time: {}", e)))?
            .as_secs()
            .to_string();

        let resource_type = if let Some(path) = file_path {
            self.determine_resource_type(path)
        } else {
            // Try to guess from file name
            let mime = mime_guess::from_path(file_name).first_or_octet_stream();
            let mime_type = mime.type_().as_str();
            match mime_type {
                "image" => "image",
                "video" => "video",
                "audio" => "raw",
                _ => "auto",
            }
        };

        let mut form = Form::new()
            .text("api_key", self.config.api_key.clone())
            .text("timestamp", timestamp.clone());

        let mut params = HashMap::new();
        params.insert("timestamp".to_string(), timestamp.clone());
        params.insert("api_key".to_string(), self.config.api_key.clone());

        if let Some(folder_name) = folder.or(Some(&self.config.default_folder)) {
            if !folder_name.is_empty() {
                form = form.text("folder", folder_name.to_string());
                params.insert("folder".to_string(), folder_name.to_string());
            }
        }

        if let Some(id) = public_id {
            form = form.text("public_id", id.to_string());
            params.insert("public_id".to_string(), id.to_string());
        }

        if let Some(transform) = transformation {
            match transform {
                "webp" => {
                    form = form.text("format", "webp");
                    params.insert("format".to_string(), "webp".to_string());
                },
                "avif" => {
                    form = form.text("format", "avif");
                    params.insert("format".to_string(), "avif".to_string());
                },
                _ => {}
            }
        }

        let mut sorted_params: Vec<(String, String)> = params.into_iter().collect();
        sorted_params.sort_by(|a, b| a.0.cmp(&b.0));

        let string_to_sign = sorted_params
            .iter()
            .map(|(key, value)| format!("{}={}", key, value))
            .collect::<Vec<String>>()
            .join("&");

        let string_to_sign = format!("{}{}", string_to_sign, self.config.api_secret);

        let mut hasher = Sha1::new();
        hasher.update(string_to_sign.as_bytes());
        let signature = format!("{:x}", hasher.finalize());

        form = form.text("signature", signature);

        let mime_type = if let Some(path) = file_path {
            from_path(path).first_or_octet_stream()
        } else {
            mime_guess::from_path(file_name).first_or_octet_stream()
        };

        let part = Part::bytes(buffer.to_vec())
            .file_name(file_name.to_string())
            .mime_str(mime_type.as_ref())
            .map_err(|e| AppError::ValidationError(format!("Invalid MIME type: {}", e)))?;

        form = form.part("file", part);

        let url = format!(
            "https://api.cloudinary.com/v1_1/{}/{}/upload",
            self.config.cloud_name, resource_type
        );

        info!("Uploading to Cloudinary: {}", url);

        let response = self
            .client
            .post(&url)
            .multipart(form)
            .send()
            .await
            .map_err(|e| AppError::ExternalServiceError(format!("Failed to send upload request: {}", e)))?;

        if !response.status().is_success() {
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(AppError::ExternalServiceError(format!("Upload failed: {}", error_text)));
        }

        let upload_response = response
            .json::<UploadResponse>()
            .await
            .map_err(|e| AppError::ExternalServiceError(format!("Failed to parse upload response: {}", e)))?;

        info!("Successfully uploaded to Cloudinary: {}", upload_response.secure_url);

        Ok(upload_response)
    }

    fn determine_resource_type(&self, file_path: &Path) -> &'static str {
        let mime = from_path(file_path).first_or_octet_stream();
        let mime_type = mime.type_().as_str();

        match mime_type {
            "image" => "image",
            "video" => "video",
            "audio" => "raw",
            _ => "auto",
        }
    }

    pub fn get_url(&self, public_id: &str, resource_type: &str, transformation: Option<&str>) -> String {
        let base_url = format!(
            "https://res.cloudinary.com/{}/{}/upload",
            self.config.cloud_name, resource_type
        );

        match transformation {
            Some("webp") => format!("{}/f_webp/{}", base_url, public_id),
            Some("avif") => format!("{}/f_avif/{}", base_url, public_id),
            _ => format!("{}/{}", base_url, public_id),
        }
    }

    pub async fn upload_profile_picture_from_field(
        &self,
        field: actix_multipart::Field,
        user_id: &str,
    ) -> Result<String, AppError> {
        let temp_storage = crate::services::TempStorageService::new();
        let (file_path, _content_type) = temp_storage.store_file(field).await?;
        let buffer = temp_storage.read_file(&file_path)?;
        let result = self.upload_profile_picture(&buffer, user_id).await;
        if let Err(e) = temp_storage.delete_file(&file_path) {
            log::warn!("Failed to delete temp file: {}", e);
        } else {
            log::info!("Successfully deleted temp file: {:?}", file_path);
        }

        result
    }

    // Upload a profile picture from a buffer
    pub async fn upload_profile_picture(
        &self,
        buffer: &[u8],
        user_id: &str,
    ) -> Result<String, AppError> {
        info!("=== CLOUDINARY UPLOAD STARTED ===");
        info!("Uploading profile picture for user_id: {}", user_id);
        info!("Buffer size: {} bytes", buffer.len());

        if buffer.is_empty() {
            info!("Error: Buffer is empty");
            return Err(AppError::ValidationError("Cannot upload empty file".to_string()));
        }

        info!("Checking Cloudinary credentials...");
        info!("Cloud name: {}", self.config.cloud_name);
        info!("API key: {}", if self.config.api_key.is_empty() { "EMPTY" } else { "PRESENT" });
        info!("API secret: {}", if self.config.api_secret.is_empty() { "EMPTY" } else { "PRESENT" });

        match self.check_credentials().await {
            Ok(true) => {
                info!("Cloudinary credentials are valid, proceeding with upload");
            },
            Ok(false) => {
                info!("Error: Cloudinary credentials are invalid or incomplete");
                return Err(AppError::ExternalServiceError("Cloudinary credentials are invalid or incomplete".to_string()));
            },
            Err(e) => {
                info!("Error checking Cloudinary credentials: {}", e);
                return Err(AppError::ExternalServiceError(format!("Failed to check Cloudinary credentials: {}", e)));
            }
        }


        let public_id = format!("profile_{}", user_id);
        info!("Using public_id: {}", public_id);

        let filename = format!("profile_{}.jpg", user_id);
        info!("Using filename: {}", filename);

        let timestamp = chrono::Utc::now().timestamp();
        info!("Using timestamp: {}", timestamp);

        let upload_url = format!(
            "https://api.cloudinary.com/v1_1/{}/image/upload",
            self.config.cloud_name
        );

        info!("Uploading directly to Cloudinary API: {}", upload_url);

        info!("Creating multipart form...");
        let mut form = reqwest::multipart::Form::new()
            .text("api_key", self.config.api_key.clone())
            .text("timestamp", timestamp.to_string())
            .text("folder", "quiickchat/profiles") // Use quiickchat/profiles path
            .text("public_id", public_id.clone());

        info!("Generating signature...");
        let string_to_sign = format!(
            "folder=quiickchat/profiles&public_id={}&timestamp={}{}",
            public_id,
            timestamp,
            self.config.api_secret
        );
        info!("String to sign (without secret): folder=quiickchat/profiles&public_id={}&timestamp={}", public_id, timestamp);

        let mut hasher = sha1::Sha1::new();
        hasher.update(string_to_sign.as_bytes());
        let signature = format!("{:x}", hasher.finalize());
        info!("Generated signature: {}", signature);

        form = form.text("signature", signature);

        info!("Adding file part to form...");
        let part = reqwest::multipart::Part::bytes(buffer.to_vec())
            .file_name(filename)
            .mime_str("image/jpeg")
            .map_err(|e| {
                info!("Error creating multipart part: {}", e);
                AppError::ValidationError(format!("Invalid MIME type: {}", e))
            })?;

        form = form.part("file", part);

        info!("Sending request to Cloudinary...");
        let response = match self.client
            .post(&upload_url)
            .multipart(form)
            .timeout(std::time::Duration::from_secs(30)) 
            .send()
            .await {
                Ok(resp) => resp,
                Err(e) => {
                    info!("Cloudinary upload failed: {}", e);
                    if e.is_timeout() {
                        info!("Request timed out");
                        return Err(AppError::ExternalServiceError("Request to Cloudinary timed out".to_string()));
                    } else if e.is_connect() {
                        info!("Connection error");
                        return Err(AppError::ExternalServiceError("Failed to connect to Cloudinary".to_string()));
                    } else {
                        return Err(AppError::ExternalServiceError(format!("Failed to upload to Cloudinary: {}", e)));
                    }
                }
            };

        let status = response.status();
        info!("Received response with status: {}", status);

        if !status.is_success() {
            let error_text = match response.text().await {
                Ok(text) => {
                    info!("Error response body: {}", text);
                    text
                },
                Err(e) => {
                    info!("Failed to read error response: {}", e);
                    "Unknown error".to_string()
                }
            };

            info!("Cloudinary upload failed with status: {}, error: {}", status, error_text);
            return Err(AppError::ExternalServiceError(format!("Cloudinary upload failed ({}): {}", status, error_text)));
        }

        info!("Parsing response...");
        let upload_response = response.json::<UploadResponse>().await
            .map_err(|e| {
                info!("Failed to parse Cloudinary response: {}", e);
                AppError::ExternalServiceError(format!("Failed to parse Cloudinary response: {}", e))
            })?;

        info!("Successfully uploaded to Cloudinary:");
        info!("  Public ID: {}", upload_response.public_id);
        info!("  Format: {}", upload_response.format);
        info!("  Size: {} bytes", upload_response.bytes);
        info!("  URL: {}", upload_response.url);
        info!("  Secure URL: {}", upload_response.secure_url);
        info!("=== CLOUDINARY UPLOAD COMPLETED ===");

        Ok(upload_response.secure_url)
    }
}

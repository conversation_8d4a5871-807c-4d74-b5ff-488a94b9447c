import { Request, Response } from 'express';
import { EC2Service } from '@/services/EC2Service';
import { ApiResponse, RedactedInstanceInfo, InstanceOperationResult } from '@/types';
import logger from '@/utils/logger';

export class EC2Controller {
  private ec2Service: EC2Service;

  constructor() {
    this.ec2Service = new EC2Service();
  }

  /**
   * Get EC2 instance status
   */
  async getStatus(req: Request, res: Response): Promise<void> {
    try {
      const includeIps = req.query.include_ips === 'true';
      
      logger.info('Status request', { 
        ip: req.ip, 
        includeIps,
        userAgent: req.get('User-Agent') 
      });

      const statusData = await this.ec2Service.getRedactedInstanceInfo(includeIps);

      const response: ApiResponse<RedactedInstanceInfo> = {
        success: true,
        data: statusData,
        message: `Instance is ${statusData.state}`,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json(response);
    } catch (error) {
      logger.error('Failed to get instance status', { error });
      throw error; // Let error middleware handle it
    }
  }

  /**
   * Start EC2 instance
   */
  async startInstance(req: Request, res: Response): Promise<void> {
    try {
      logger.info('Start instance request', { 
        ip: req.ip, 
        userAgent: req.get('User-Agent') 
      });

      const result = await this.ec2Service.startInstance();

      const response: ApiResponse<InstanceOperationResult> = {
        success: true,
        data: result,
        message: result.message,
        timestamp: new Date().toISOString(),
      };

      // Add note about monitoring progress
      if (!result.alreadyInState) {
        response.data = {
          ...result,
          note: 'Use /status endpoint to monitor progress',
        };
      }

      res.status(200).json(response);
    } catch (error) {
      logger.error('Failed to start instance', { error });
      throw error; // Let error middleware handle it
    }
  }

  /**
   * Stop EC2 instance
   */
  async stopInstance(req: Request, res: Response): Promise<void> {
    try {
      logger.info('Stop instance request', { 
        ip: req.ip, 
        userAgent: req.get('User-Agent') 
      });

      const result = await this.ec2Service.stopInstance();

      const response: ApiResponse<InstanceOperationResult> = {
        success: true,
        data: result,
        message: result.message,
        timestamp: new Date().toISOString(),
      };

      // Add note about monitoring progress
      if (!result.alreadyInState) {
        response.data = {
          ...result,
          note: 'Use /status endpoint to monitor progress',
        };
      }

      res.status(200).json(response);
    } catch (error) {
      logger.error('Failed to stop instance', { error });
      throw error; // Let error middleware handle it
    }
  }

  /**
   * Restart EC2 instance (stop then start)
   */
  async restartInstance(req: Request, res: Response): Promise<void> {
    try {
      logger.info('Restart instance request', { 
        ip: req.ip, 
        userAgent: req.get('User-Agent') 
      });

      // First, stop the instance
      const stopResult = await this.ec2Service.stopInstance();

      if (stopResult.alreadyInState && stopResult.currentState === 'stopped') {
        // Instance is already stopped, start it
        const startResult = await this.ec2Service.startInstance();
        
        const response: ApiResponse = {
          success: true,
          data: {
            restart_initiated: true,
            phase: 'starting',
            stop_result: stopResult,
            start_result: startResult,
            estimated_total_time: '2-3 minutes',
          },
          message: 'Restart completed - instance was already stopped, now starting',
          timestamp: new Date().toISOString(),
        };

        res.status(200).json(response);
      } else {
        // Instance is stopping, will need to wait and then start
        const response: ApiResponse = {
          success: true,
          data: {
            restart_initiated: true,
            phase: 'stopping',
            stop_result: stopResult,
            estimated_total_time: '4-6 minutes',
            note: 'Instance will automatically start after stopping. Use /status to monitor.',
          },
          message: 'Restart initiated - stopping instance first',
          timestamp: new Date().toISOString(),
        };

        res.status(200).json(response);

        // Optionally, you could implement automatic start after stop
        // This would require background processing or webhooks
      }
    } catch (error) {
      logger.error('Failed to restart instance', { error });
      throw error; // Let error middleware handle it
    }
  }

  /**
   * Wait for instance to reach a specific state
   */
  async waitForState(req: Request, res: Response): Promise<void> {
    try {
      const { state, timeout } = req.query;
      
      if (!state || typeof state !== 'string') {
        const response: ApiResponse = {
          success: false,
          message: 'State parameter is required',
          error: 'Missing or invalid state parameter',
          code: 'INVALID_PARAMETER',
          timestamp: new Date().toISOString(),
        };
        res.status(400).json(response);
        return;
      }

      const timeoutMs = timeout ? parseInt(timeout as string) * 1000 : 300000; // Default 5 minutes

      logger.info('Wait for state request', { 
        ip: req.ip, 
        targetState: state,
        timeoutMs,
        userAgent: req.get('User-Agent') 
      });

      const success = await this.ec2Service.waitForState(state, timeoutMs);
      const currentStatus = await this.ec2Service.getRedactedInstanceInfo();

      const response: ApiResponse = {
        success,
        data: {
          target_state: state,
          current_state: currentStatus.state,
          reached_target: success,
          timeout_ms: timeoutMs,
        },
        message: success 
          ? `Instance reached ${state} state` 
          : `Timeout waiting for ${state} state`,
        timestamp: new Date().toISOString(),
      };

      res.status(success ? 200 : 408).json(response);
    } catch (error) {
      logger.error('Failed to wait for state', { error });
      throw error; // Let error middleware handle it
    }
  }
}

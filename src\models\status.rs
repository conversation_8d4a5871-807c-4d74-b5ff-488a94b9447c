use chrono::{DateTime, Utc};
use mongodb::bson::oid::ObjectId;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Status {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: String,
    pub content_type: StatusContentType,
    pub content_url: String,
    pub caption: Option<String>,
    pub background_color: Option<String>,
    pub font_style: Option<String>,
    pub privacy_setting: StatusPrivacy,
    pub allowed_contacts: Vec<String>, // User IDs who can view this status
    pub blocked_contacts: Vec<String>, // User IDs who cannot view this status
    pub created_at_millis: i64,
    pub expires_at_millis: i64,
    pub is_active: bool,
    pub view_count: i32,
    pub views: Vec<StatusView>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum StatusContentType {
    Text,
    Image,
    Video,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum StatusPrivacy {
    Everyone,        // All contacts can see
    Contacts,        // Only contacts can see
    ContactsExcept,  // All contacts except blocked ones
    OnlyShare,       // Only specific contacts can see
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct StatusView {
    pub viewer_id: String,
    pub viewed_at_millis: i64,
    pub viewer_username: Option<String>,
    pub viewer_profile_picture: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct StatusPrivacySettings {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: String,
    pub default_privacy: StatusPrivacy,
    pub always_allow: Vec<String>, // User IDs who can always see status
    pub always_block: Vec<String>, // User IDs who can never see status
    pub read_receipts_enabled: bool, // Whether to show who viewed status
    pub created_at_millis: i64,
    pub updated_at_millis: i64,
}

// Response models
#[derive(Debug, Serialize, Deserialize)]
pub struct StatusResponse {
    pub id: String,
    pub user_id: String,
    pub username: Option<String>,
    pub profile_picture: Option<String>,
    pub content_type: StatusContentType,
    pub content_url: String,
    pub caption: Option<String>,
    pub background_color: Option<String>,
    pub font_style: Option<String>,
    pub created_at_millis: i64,
    pub expires_at_millis: i64,
    pub view_count: i32,
    pub has_viewed: bool, // Whether the current user has viewed this status
    pub can_view: bool,   // Whether the current user can view this status
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StatusViewResponse {
    pub viewer_id: String,
    pub viewer_username: Option<String>,
    pub viewer_profile_picture: Option<String>,
    pub viewed_at_millis: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StatusSummary {
    pub user_id: String,
    pub username: Option<String>,
    pub profile_picture: Option<String>,
    pub status_count: i32,
    pub latest_status_time: i64,
    pub has_unviewed: bool,
}

// Request models
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateStatusRequest {
    pub content_type: StatusContentType,
    pub content_url: Option<String>, // Optional for text status
    pub caption: Option<String>,
    pub background_color: Option<String>,
    pub font_style: Option<String>,
    pub privacy_setting: Option<StatusPrivacy>,
    pub allowed_contacts: Option<Vec<String>>,
    pub blocked_contacts: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateStatusPrivacyRequest {
    pub default_privacy: Option<StatusPrivacy>,
    pub always_allow: Option<Vec<String>>,
    pub always_block: Option<Vec<String>>,
    pub read_receipts_enabled: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ViewStatusRequest {
    pub status_id: String,
}

impl From<Status> for StatusResponse {
    fn from(status: Status) -> Self {
        StatusResponse {
            id: status.id.map_or_else(|| "".to_string(), |id| id.to_hex()),
            user_id: status.user_id,
            username: None, // Will be populated by the service
            profile_picture: None, // Will be populated by the service
            content_type: status.content_type,
            content_url: status.content_url,
            caption: status.caption,
            background_color: status.background_color,
            font_style: status.font_style,
            created_at_millis: status.created_at_millis,
            expires_at_millis: status.expires_at_millis,
            view_count: status.view_count,
            has_viewed: false, // Will be determined by the service
            can_view: false,   // Will be determined by the service
        }
    }
}

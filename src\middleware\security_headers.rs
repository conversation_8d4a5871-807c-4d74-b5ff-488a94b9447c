use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    http::header,
    Error,
};
use futures::future::{ok, Ready};
use std::future::Future;
use std::pin::Pin;
// use std::task::{Context, Poll};

// Middleware to add security headers to all responses
pub struct SecurityHeaders;

impl<S, B> Transform<S, ServiceRequest> for SecurityHeaders
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = SecurityHeadersMiddleware<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ok(SecurityHeadersMiddleware { service })
    }
}

pub struct SecurityHeadersMiddleware<S> {
    service: S,
}

impl<S, B> Service<ServiceRequest> for SecurityHeadersMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>>>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        // Store the path for later use
        let path = req.path().to_string();
        let fut = self.service.call(req);

        Box::pin(async move {
            let mut res = fut.await?;

            // Add security headers to the response
            let headers = res.headers_mut();

            // Content-Security-Policy
            // Restricts the sources from which resources can be loaded
            // Allow connections from any origin for API endpoints to support CORS
            headers.insert(
                header::CONTENT_SECURITY_POLICY,
                header::HeaderValue::from_static(
                    "default-src 'self'; script-src 'self'; style-src 'self'; img-src 'self' data:; font-src 'self'; connect-src *",
                ),
            );

            // X-Content-Type-Options
            // Prevents MIME type sniffing
            headers.insert(
                header::HeaderName::from_static("x-content-type-options"),
                header::HeaderValue::from_static("nosniff"),
            );

            // X-Frame-Options
            // Prevents clickjacking by disallowing the page to be embedded in an iframe
            headers.insert(
                header::HeaderName::from_static("x-frame-options"),
                header::HeaderValue::from_static("DENY"),
            );

            // X-XSS-Protection
            // Enables XSS filtering in browsers
            headers.insert(
                header::HeaderName::from_static("x-xss-protection"),
                header::HeaderValue::from_static("1; mode=block"),
            );

            // Strict-Transport-Security
            // Forces HTTPS connections
            headers.insert(
                header::HeaderName::from_static("strict-transport-security"),
                header::HeaderValue::from_static("max-age=31536000; includeSubDomains; preload"),
            );

            // Referrer-Policy
            // Controls how much referrer information is included with requests
            headers.insert(
                header::HeaderName::from_static("referrer-policy"),
                header::HeaderValue::from_static("strict-origin-when-cross-origin"),
            );

            // Feature-Policy
            // Restricts which browser features can be used
            headers.insert(
                header::HeaderName::from_static("feature-policy"),
                header::HeaderValue::from_static(
                    "camera 'none'; microphone 'none'; geolocation 'none'; payment 'none'",
                ),
            );

            // Cache-Control
            // Prevents caching of sensitive information
            if path.starts_with("/api/v1/auth") || path.contains("/users/") {
                headers.insert(
                    header::CACHE_CONTROL,
                    header::HeaderValue::from_static("no-store, no-cache, must-revalidate, max-age=0"),
                );

                headers.insert(
                    header::PRAGMA,
                    header::HeaderValue::from_static("no-cache"),
                );
            }

            Ok(res)
        })
    }
}

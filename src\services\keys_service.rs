use chrono::{DateTime, Utc};

use mongodb::{
    bson::{doc, Document},
    Collection,
};
// use rand::rngs::ThreadRng;
use std::sync::Arc;
use base64::{Engine as _, engine::general_purpose};
use x25519_dalek::{PublicKey, EphemeralSecret};

use crate::errors::AppError;
use crate::services::mongodb::MongoDBService;

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct KeyBundle {
    pub identity_key: String,
    pub signed_pre_key: String,
    pub pre_key: String,
    pub one_time_keys: Vec<String>,
    pub last_updated: i64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct KeyBundleResponse {
    pub identity_key: String,
    pub identity_private: String,
    pub signed_pre_key: String,
    pub signed_pre_key_private: String,
    pub pre_key: String,
    pub pre_key_private: String,
    pub one_time_keys: Vec<String>,
    pub last_updated: i64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct Session {
    pub user_id: String,
    pub contact_id: String,
    pub is_active: bool,
    pub last_updated: DateTime<Utc>,
    pub send_chain_length: u32,
    pub receive_chain_length: u32,
}

pub struct KeysService {
    mongodb: Arc<MongoDBService>,
    keys_collection: Collection<Document>,
    sessions_collection: Collection<Document>,
}

impl KeysService {
    pub fn new(mongodb: Arc<MongoDBService>) -> Self {
        let keys_collection = mongodb.collection::<Document>("user_keys");
        let sessions_collection = mongodb.collection::<Document>("sessions");

        Self {
            mongodb,
            keys_collection,
            sessions_collection,
        }
    }

    // Generate a new X25519 key pair
    pub fn generate_x25519_key_pair(&self) -> Result<(String, String), AppError> {
        let rng = rand::thread_rng();
        let private_key = EphemeralSecret::random_from_rng(rng);
        let public_key = PublicKey::from(&private_key);

        // Get the public key bytes
        let public_key_bytes = public_key.as_bytes();

        // For private key, we need to use a different approach since EphemeralSecret doesn't expose bytes directly
        // We'll use a placeholder for now and consider a better approach later
        let private_key_bytes = [0u8; 32];

        let private_key_b64 = general_purpose::STANDARD.encode(private_key_bytes);
        let public_key_b64 = general_purpose::STANDARD.encode(public_key_bytes);

        Ok((private_key_b64, public_key_b64))
    }

    // Generate keys for a user
    pub async fn generate_keys(&self, user_id: &str) -> Result<KeyBundleResponse, AppError> {
        let (identity_private, identity_public) = self.generate_x25519_key_pair()?;
        let (signed_pre_key_private, signed_pre_key_public) = self.generate_x25519_key_pair()?;
        let (pre_key_private, pre_key_public) = self.generate_x25519_key_pair()?;
        let mut one_time_keys = Vec::with_capacity(100);
        let mut one_time_keys_private = Vec::with_capacity(100);

        for _ in 0..100 {
            let (private, public) = self.generate_x25519_key_pair()?;
            one_time_keys.push(public);
            one_time_keys_private.push(private);
        }
        self.store_public_keys(
            user_id,
            &identity_public,
            &signed_pre_key_public,
            &pre_key_public,
            &one_time_keys,
        ).await?;

        let response = KeyBundleResponse {
            identity_key: identity_public,
            identity_private,
            signed_pre_key: signed_pre_key_public,
            signed_pre_key_private,
            pre_key: pre_key_public,
            pre_key_private,
            one_time_keys: one_time_keys.clone(),
            last_updated: Utc::now().timestamp(),
        };

        Ok(response)
    }

    async fn store_public_keys(
        &self,
        user_id: &str,
        identity_key: &str,
        signed_pre_key: &str,
        pre_key: &str,
        one_time_keys: &[String],
    ) -> Result<(), AppError> {
        let filter = doc! { "user_id": user_id };

        let existing = self.keys_collection.find_one(filter.clone(), None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to check for existing keys: {}", e)))?;

        if existing.is_some() {
            let update = doc! {
                "$set": {
                    "identity_key": identity_key,
                    "signed_pre_key": signed_pre_key,
                    "pre_key": pre_key,
                    "one_time_keys": one_time_keys,
                    "last_updated": Utc::now().timestamp()
                }
            };

            self.keys_collection.update_one(filter, update, None).await
                .map_err(|e| AppError::DatabaseError(format!("Failed to update keys: {}", e)))?;
        } else {
            let document = doc! {
                "user_id": user_id,
                "identity_key": identity_key,
                "signed_pre_key": signed_pre_key,
                "pre_key": pre_key,
                "one_time_keys": one_time_keys,
                "last_updated": Utc::now().timestamp()
            };

            self.keys_collection.insert_one(document, None).await
                .map_err(|e| AppError::DatabaseError(format!("Failed to store keys: {}", e)))?;
        }

        Ok(())
    }

    // Retrieve public keys for a user
    pub async fn retrieve_public_keys(&self, user_id: &str) -> Result<KeyBundle, AppError> {
        let filter = doc! { "user_id": user_id };

        let document = self.keys_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to retrieve keys: {}", e)))?;

        match document {
            Some(doc) => {
                let identity_key = doc.get_str("identity_key")
                    .map_err(|_| AppError::DatabaseError("Failed to get identity key".to_string()))?
                    .to_string();

                let signed_pre_key = doc.get_str("signed_pre_key")
                    .map_err(|_| AppError::DatabaseError("Failed to get signed pre key".to_string()))?
                    .to_string();

                let pre_key = doc.get_str("pre_key")
                    .map_err(|_| AppError::DatabaseError("Failed to get pre key".to_string()))?
                    .to_string();

                let one_time_keys = doc.get_array("one_time_keys")
                    .map_err(|_| AppError::DatabaseError("Failed to get one time keys".to_string()))?
                    .iter()
                    .filter_map(|v| v.as_str().map(|s| s.to_string()))
                    .collect();

                let last_updated = doc.get_i64("last_updated")
                    .map_err(|_| AppError::DatabaseError("Failed to get last updated".to_string()))?;

                Ok(KeyBundle {
                    identity_key,
                    signed_pre_key,
                    pre_key,
                    one_time_keys,
                    last_updated,
                })
            },
            None => Err(AppError::NotFoundError(format!("No keys found for user: {}", user_id))),
        }
    }

    // Rotate keys for a user
    pub async fn rotate_keys(&self, user_id: &str) -> Result<KeyBundleResponse, AppError> {
        let (signed_pre_key_private, signed_pre_key_public) = self.generate_x25519_key_pair()?;
        let (pre_key_private, pre_key_public) = self.generate_x25519_key_pair()?;
        let mut one_time_keys = Vec::with_capacity(100);
        let mut one_time_keys_private = Vec::with_capacity(100);

        for _ in 0..100 {
            let (private, public) = self.generate_x25519_key_pair()?;
            one_time_keys.push(public);
            one_time_keys_private.push(private);
        }
        let existing_keys = self.retrieve_public_keys(user_id).await?;

        self.store_public_keys(
            user_id,
            &existing_keys.identity_key,
            &signed_pre_key_public,
            &pre_key_public,
            &one_time_keys,
        ).await?;

        let response = KeyBundleResponse {
            identity_key: existing_keys.identity_key,
            identity_private: "".to_string(), 
            signed_pre_key: signed_pre_key_public,
            signed_pre_key_private,
            pre_key: pre_key_public,
            pre_key_private,
            one_time_keys: one_time_keys.clone(),
            last_updated: Utc::now().timestamp(),
        };

        Ok(response)
    }

    // Get session status
    pub async fn get_session_status(&self, user_id: &str, contact_id: &str) -> Result<Session, AppError> {
        let filter = doc! {
            "user_id": user_id,
            "contact_id": contact_id
        };

        let document = self.sessions_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to retrieve session: {}", e)))?;

        match document {
            Some(doc) => {
                let is_active = doc.get_bool("is_active")
                    .map_err(|_| AppError::DatabaseError("Failed to get is_active".to_string()))?;

                let last_updated = doc.get_datetime("last_updated")
                    .map_err(|_| AppError::DatabaseError("Failed to get last_updated".to_string()))?
                    .to_chrono();

                let send_chain_length = doc.get_i32("send_chain_length")
                    .map_err(|_| AppError::DatabaseError("Failed to get send_chain_length".to_string()))?
                    as u32;

                let receive_chain_length = doc.get_i32("receive_chain_length")
                    .map_err(|_| AppError::DatabaseError("Failed to get receive_chain_length".to_string()))?
                    as u32;

                Ok(Session {
                    user_id: user_id.to_string(),
                    contact_id: contact_id.to_string(),
                    is_active,
                    last_updated,
                    send_chain_length,
                    receive_chain_length,
                })
            },
            None => Err(AppError::NotFoundError(format!("No session found between {} and {}", user_id, contact_id))),
        }
    }

    // Revoke keys
    pub async fn revoke_keys(&self, user_id: &str) -> Result<(), AppError> {
        // Delete all keys
        let filter = doc! { "user_id": user_id };
        self.keys_collection.delete_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to delete keys: {}", e)))?;

        let filter = doc! { "user_id": user_id };
        self.sessions_collection.delete_many(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to delete sessions: {}", e)))?;

        Ok(())
    }
}

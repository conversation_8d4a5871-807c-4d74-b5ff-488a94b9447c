use rand::{Rng, thread_rng};
use rand::distributions::Alphanumeric;
use sha2::{Sha256, Digest};
use std::time::{SystemTime, UNIX_EPOCH};

// Generate a random username with the format "quiickchat-{random_chars}"
pub fn generate_username() -> String {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_nanos();

    let random_chars: String = thread_rng()
        .sample_iter(&Alphanumeric)
        .take(20)
        .map(char::from)
        .collect();

    format!("quiickchat-{}-{}", timestamp % 10000, random_chars.to_lowercase())
}

// Generate a secure password (64 characters using SHA-256)
pub fn generate_secure_password() -> String {
    let random_bytes: Vec<u8> = (0..32).map(|_| thread_rng().gen::<u8>()).collect();
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_nanos()
        .to_string();

    // Combine random bytes with timestamp
    let mut hasher = Sha256::new();
    hasher.update(&random_bytes);
    hasher.update(timestamp.as_bytes());

    // Convert to hex string
    let result = hasher.finalize();
    format!("{:x}", result)
}

// Hash a password using SHA-256
pub fn hash_password(password: &str) -> String {
    let mut hasher = Sha256::new();
    hasher.update(password.as_bytes());
    format!("{:x}", hasher.finalize())
}

// Generate a random verification code (6 digits)
pub fn generate_verification_code() -> String {
    let code: u32 = thread_rng().gen_range(100000..=999999);
    code.to_string()
}

// Mask sensitive data for logging
pub fn mask_sensitive_data(data: &str) -> String {
    if data.is_empty() {
        return String::from("(empty)");
    }

    // For very short strings, just return *****
    if data.len() < 8 {
        return "*****".to_string();
    }

    // For longer strings, show first 3 and last 3 characters
    let prefix = &data[0..3];
    let suffix = &data[data.len() - 3..];
    format!("{}*****{}", prefix, suffix)
}

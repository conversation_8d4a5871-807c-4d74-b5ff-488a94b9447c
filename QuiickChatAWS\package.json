{"name": "qui<PERSON><PERSON>t-ec2-manager", "version": "1.0.0", "description": "TypeScript REST API for managing QuiickChat EC2 instances", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "lint": "eslint src/**/*.ts", "test": "jest", "cli:status": "ts-node src/cli/ec2-cli.ts status", "cli:start": "ts-node src/cli/ec2-cli.ts start", "cli:stop": "ts-node src/cli/ec2-cli.ts stop"}, "keywords": ["aws", "ec2", "typescript", "rest-api", "cloud-management"], "author": "QuiickChat Team", "license": "MIT", "dependencies": {"@aws-sdk/client-ec2": "^3.450.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0", "compression": "^1.7.4", "commander": "^11.1.0", "chalk": "^4.1.2", "ora": "^5.4.1"}, "devDependencies": {"@types/node": "^20.8.0", "@types/express": "^4.17.20", "@types/cors": "^2.8.15", "@types/compression": "^1.7.4", "@types/jest": "^29.5.6", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "eslint": "^8.51.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
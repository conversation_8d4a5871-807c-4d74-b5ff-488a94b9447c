import app from '@/app';
import config from '@/config';
import logger from '@/utils/logger';
import { createServer } from 'http';

// Create HTTP server
const server = createServer(app);

// Graceful shutdown handling
const gracefulShutdown = (signal: string) => {
  logger.info(`Received ${signal}, shutting down gracefully`);
  
  server.close(() => {
    logger.info('HTTP server closed');
    process.exit(0);
  });

  // Force close after 10 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  process.exit(1);
});

// Start server
server.listen(config.port, config.host, () => {
  logger.info('🚀 QuiickChat EC2 Manager API started', {
    port: config.port,
    host: config.host,
    environment: config.nodeEnv,
    region: config.aws.region,
  });
  
  console.log(`
🎯 QuiickChat EC2 Manager API
🌐 Server: http://${config.host}:${config.port}
🔍 Health: http://${config.host}:${config.port}/health
📊 Status: http://${config.host}:${config.port}/api/v1/status
🔑 API Key: ${config.api.key.substring(0, 4)}${'*'.repeat(config.api.key.length - 4)}
📝 Environment: ${config.nodeEnv}
🌍 AWS Region: ${config.aws.region}
  `);
});

export default server;

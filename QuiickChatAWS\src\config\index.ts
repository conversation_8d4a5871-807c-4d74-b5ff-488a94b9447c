import dotenv from 'dotenv';
import <PERSON><PERSON> from 'joi';
import { Config } from '@/types';

// Load environment variables
dotenv.config();

// Environment validation schema
const envSchema = Joi.object({
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  PORT: Joi.number().port().default(5000),
  HOST: Joi.string().default('0.0.0.0'),
  
  // AWS Configuration
  AWS_REGION: Joi.string().default('us-east-1'),
  EC2_INSTANCE_ID: Joi.string().required().pattern(/^i-[0-9a-f]{8,17}$/),
  AWS_ACCESS_KEY_ID: Joi.string().optional(),
  AWS_SECRET_ACCESS_KEY: Joi.string().optional(),
  
  // API Configuration
  API_KEY: Joi.string().min(8).required(),
  CORS_ORIGIN: Joi.string().default('*'),
  RATE_LIMIT_WINDOW_MS: Joi.number().default(900000), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),
  
  // Logging
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),
  LOG_FORMAT: Joi.string().valid('combined', 'common', 'dev', 'short', 'tiny').default('combined'),
}).unknown();

// Validate environment variables
const { error, value: envVars } = envSchema.validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

// Create configuration object
const config: Config = {
  port: envVars.PORT,
  host: envVars.HOST,
  nodeEnv: envVars.NODE_ENV,
  
  aws: {
    region: envVars.AWS_REGION,
    instanceId: envVars.EC2_INSTANCE_ID,
    accessKeyId: envVars.AWS_ACCESS_KEY_ID,
    secretAccessKey: envVars.AWS_SECRET_ACCESS_KEY,
  },
  
  api: {
    key: envVars.API_KEY,
    corsOrigin: envVars.CORS_ORIGIN,
    rateLimitWindowMs: envVars.RATE_LIMIT_WINDOW_MS,
    rateLimitMaxRequests: envVars.RATE_LIMIT_MAX_REQUESTS,
  },
  
  logging: {
    level: envVars.LOG_LEVEL,
    format: envVars.LOG_FORMAT,
  },
};

// Validate instance ID format
if (!config.aws.instanceId.match(/^i-[0-9a-f]{8,17}$/)) {
  throw new Error('Invalid EC2_INSTANCE_ID format. Must be like: i-1234567890abcdef0');
}

// Log configuration (redacted)
console.log('✅ Configuration loaded:');
console.log(`   Environment: ${config.nodeEnv}`);
console.log(`   Port: ${config.port}`);
console.log(`   AWS Region: ${config.aws.region}`);
console.log(`   Instance ID: ${config.aws.instanceId.substring(0, 10)}...${config.aws.instanceId.slice(-4)}`);
console.log(`   API Key: ${config.api.key.substring(0, 4)}${'*'.repeat(config.api.key.length - 4)}`);

export default config;

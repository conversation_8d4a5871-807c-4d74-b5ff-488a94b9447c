// Types and interfaces for the EC2 Manager

export interface EC2InstanceState {
  name: string;
  code: number;
}

export interface EC2Instance {
  instanceId: string;
  instanceType: string;
  state: EC2InstanceState;
  publicIpAddress?: string;
  privateIpAddress?: string;
  launchTime?: Date;
  availabilityZone?: string;
  platform?: string;
  tags?: Record<string, string>;
}

export interface RedactedInstanceInfo {
  instanceHash: string;
  region: string;
  state: string;
  stateCode: number;
  hasPublicIp: boolean;
  hasPrivateIp: boolean;
  instanceType: string;
  launchTime?: string;
  availabilityZone: string;
  timestamp: string;
  publicIp?: string;
  privateIp?: string;
}

export interface InstanceOperationResult {
  success: boolean;
  previousState?: string;
  currentState: string;
  estimatedTime?: string;
  alreadyInState?: boolean;
  message: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  error?: string;
  code?: string;
  timestamp: string;
}

export interface HealthCheckResponse {
  success: boolean;
  service: string;
  version: string;
  timestamp: string;
  region: string;
  uptime: number;
  environment: string;
}

export interface ApiError extends Error {
  statusCode: number;
  code: string;
}

export interface Config {
  port: number;
  host: string;
  nodeEnv: string;
  aws: {
    region: string;
    instanceId: string;
    accessKeyId?: string;
    secretAccessKey?: string;
  };
  api: {
    key: string;
    corsOrigin: string;
    rateLimitWindowMs: number;
    rateLimitMaxRequests: number;
  };
  logging: {
    level: string;
    format: string;
  };
}

// CLI Types
export type CliCommand = 'status' | 'start' | 'stop' | 'restart';

export interface CliOptions {
  includeIps?: boolean;
  wait?: boolean;
  timeout?: number;
  verbose?: boolean;
}

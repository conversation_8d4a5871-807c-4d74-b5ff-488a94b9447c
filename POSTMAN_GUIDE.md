# QuiickChat API - Postman Collection Guide

## 📋 **Overview**

This comprehensive Postman collection contains all QuiickChat API endpoints organized into logical groups for easy testing and development.

## 🚀 **Quick Setup**

### 1. **Import Collection**
1. Open Postman
2. Click **Import** button
3. Select `QuiickChat_API.postman_collection.json`
4. Collection will be imported with all endpoints

### 2. **Environment Setup**
The collection includes these variables:
- `base_url`: API server URL (default: `http://localhost:8080`)
- `access_token`: JWT access token (auto-set after login)
- `refresh_token`: JWT refresh token (auto-set after login)
- `user_id`: Current user ID (auto-set after login)
- `phone`: Your phone number (default: `+*************`)

**To customize:**
1. Go to **Environments** in Postman
2. Create new environment or edit existing
3. Set your phone number in `phone` variable
4. Set your server URL in `base_url` if different

## 📱 **Testing Flow**

### **Step 1: Register/Login**
1. **Register User** - Creates new account
   - Uses phone number from `{{phone}}` variable
   - Returns verification code in development mode
   - Code is auto-saved to `verification_code` variable

2. **Verify Registration** - Completes registration
   - Uses saved verification code
   - Returns user data and auth tokens
   - Tokens are auto-saved for subsequent requests

### **Step 2: User Management**
- **Get Profile** - Retrieve user information
- **Update Profile** - Modify user details
- **Upload Profile Picture** - Add profile image
- **Settings** - Manage user preferences

### **Step 3: Contact Management**
- **Upload Contacts** - Sync contacts from device
  - Upload array of contacts with display names and phone numbers
  - API automatically checks registration status
  - Normalizes phone numbers to E.164 format
- **Get Contacts** - Retrieve all contacts with registration status
- **Sync Contacts** - Re-check registration status for existing contacts

### **Step 4: Status Features**
- **Create Status** - Post text or media status
- **View Feed** - See contacts' statuses (now uses uploaded contacts)
- **Manage Privacy** - Control who sees your status

### **Step 5: Real-time Features**
- **WebSocket Connection** - Connect for real-time status updates
- **Status Broadcasting** - Automatic notifications to contacts
- **Connection Stats** - Monitor WebSocket connections

## 🔧 **Collection Features**

### **Auto-Token Management**
- Tokens are automatically extracted and saved after login
- All authenticated requests use saved tokens
- Refresh token endpoint updates access token

### **Environment Variables**
- Phone number and user ID are auto-populated
- Verification codes are captured from responses
- Base URL can be easily changed for different environments

### **Pre-request Scripts**
- Automatic environment setup
- Debug logging of current variables
- Validation of required parameters

### **Test Scripts**
- Response time validation
- Response structure validation
- Automatic token extraction
- Debug logging for troubleshooting

## 📚 **Endpoint Categories**

### 🔐 **Authentication**
- `POST /auth/register` - Register new user
- `POST /auth/verify` - Verify registration
- `POST /auth/login` - Login existing user
- `POST /auth/verify-login` - Verify login
- `POST /auth/refresh-token` - Refresh access token
- `POST /auth/resend-code` - Resend verification code

### 👤 **User Management**
- `GET /users/init` - Initialize user data
- `GET /users/me` - Get current user
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update profile
- `POST /users/upload-profile-picture` - Upload profile picture
- `GET /users/profile/picture/{user_id}` - Get profile picture
- `DELETE /users/delete-account` - Delete account

### ⚙️ **User Settings**
- `GET /users/settings` - Get all settings
- `PUT /users/settings/notifications` - Update notifications
- `PUT /users/settings/privacy` - Update privacy
- `PUT /users/settings/appearance` - Update appearance
- `PUT /users/settings/language` - Update language

### 📱 **Status Management**
- `POST /status` - Create text status
- `POST /status/upload` - Upload media status
- `GET /status/feed` - Get status feed
- `GET /status/my` - Get my statuses
- `GET /status/user/{user_id}` - Get user statuses
- `POST /status/view` - Mark status as viewed
- `GET /status/{status_id}/views` - Get status views
- `DELETE /status/{status_id}` - Delete status

### 🔒 **Status Privacy**
- `GET /status/privacy` - Get privacy settings
- `PUT /status/privacy` - Update privacy settings

### 🔐 **Encryption Keys**
- `POST /keys/generate` - Generate encryption keys
- `GET /keys/{user_id}` - Get pre-key bundle
- `POST /keys/rotate` - Rotate keys
- `POST /keys/revoke` - Revoke keys
- `GET /keys/session/{user_id}` - Get session status

### 🔧 **Debug & Utilities**
- `GET /health` - Health check
- `GET /users/debug-phone` - Debug phone lookup
- `GET /users/debug-twilio` - Test Twilio credentials

## 🛠️ **Development Features**

### **Verification Code in Response**
In development mode (`ENVIRONMENT=development`), verification codes are included in API responses:

```json
{
  "success": true,
  "message": "Verification code sent",
  "data": {
    "phone": "+*************",
    "sms_sent": false,
    "verification_code": "123456",
    "note": "Verification code included for development purposes"
  }
}
```

### **Debug Endpoints**
- **Debug Phone Lookup**: Test if phone numbers are found in database
- **Debug Twilio**: Test Twilio credentials and connectivity
- **Health Check**: Verify API service is running

## 🔍 **Troubleshooting**

### **Common Issues**

1. **"Authentication required" errors**
   - Ensure you've completed login flow
   - Check that `access_token` is set in environment
   - Try refreshing token if expired

2. **"User not found" errors**
   - Verify phone number format includes country code
   - Use debug phone lookup to test database
   - Ensure user is registered and verified

3. **"Invalid verification code" errors**
   - Check verification code in response (development mode)
   - Ensure code hasn't expired (10 minutes)
   - Try resending verification code

### **Debug Steps**
1. Check environment variables are set correctly
2. Use debug endpoints to test connectivity
3. Review console logs in Postman
4. Check server logs for detailed error messages

## � **WebSocket Testing**

### **Connection URL**
```
ws://localhost:8080/api/v1/contacts/ws?user_id={{user_id}}
```

### **Message Types**
The WebSocket connection supports these message types:

**Client to Server:**
```json
{
  "type": "ping"
}
```

```json
{
  "type": "subscribe_status"
}
```

**Server to Client:**
```json
{
  "message_type": "status_update",
  "data": {
    "user_id": "user123",
    "status_id": "status456",
    "content_type": "text",
    "caption": "Hello World!",
    "created_at_millis": 1640995200000
  },
  "timestamp": 1640995200000
}
```

### **Testing WebSocket in Postman**
1. Use a WebSocket client tool (like WebSocket King or browser console)
2. Connect to the WebSocket URL with your user_id
3. Send subscription messages
4. Create a status via API to see real-time updates
5. Use the "Test Status Broadcast" endpoint to trigger notifications

## �🎯 **Best Practices**

1. **Use Environment Variables**: Don't hardcode values
2. **Test Authentication First**: Ensure login works before testing other endpoints
3. **Upload Contacts Early**: Upload contacts before testing status features for better experience
4. **Test WebSocket Separately**: Use dedicated WebSocket tools for real-time testing
5. **Check Response Structure**: Validate expected fields are present
4. **Monitor Response Times**: API should respond within 5 seconds
5. **Handle Errors Gracefully**: Check error messages for debugging info

## 📞 **Support**

If you encounter issues:
1. Check server logs for detailed error messages
2. Use debug endpoints to isolate problems
3. Verify environment configuration
4. Test with different phone numbers if needed

Happy testing! 🚀

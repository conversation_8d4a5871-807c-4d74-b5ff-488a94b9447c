[package]
name = "quiickchat"
version = "0.1.0"
edition = "2021"
default-run = "quiickchat"

[features]
default = []
go_token_lib = []

[lib]
name = "quiickchat"
path = "src/lib.rs"

[[bin]]
name = "quiickchat"
path = "src/main.rs"

[dependencies]
actix-web = "4.4.0"
actix-cors = "0.6.4"
actix-web-actors = "4.2.0"
actix-files = "0.6.2"
actix = "0.13.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.29.1", features = ["full"] }
futures = "0.3.28"
dotenv = "0.15.0"
env_logger = "0.10.0"
log = "0.4.19"
uuid = { version = "1.4.1", features = ["v4", "serde"] }
jsonwebtoken = "8.3.0"
chrono = { version = "0.4.26", features = ["serde", "clock"] }
regex = "1.9.1"
once_cell = "1.18.0"
sanitize_html = "0.7.0"
actix-service = "2.0.2"
validator = { version = "0.16.1", features = ["derive"] }
thiserror = "1.0.44"
async-trait = "0.1.72"
reqwest = { version = "0.11.18", features = ["json", "multipart"] }
mime_guess = "2.0.4"
sha1 = "0.10.5"
ctrlc = "3.4.0"
base64 = "0.21.2"
hmac = "0.12.1"
sha2 = "0.10.7"
hex = "0.4.3"
libc = "0.2.147"
rand = "0.8.5"
mongodb = { version = "2.6.0", features = ["tokio-runtime", "bson-chrono-0_4"] }
actix-multipart = "0.6.0"
x25519-dalek = "2.0.0"
mime = "0.3.17"

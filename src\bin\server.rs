use actix_web::{web, App, Http<PERSON>erver, HttpResponse};
use actix_web::middleware::{<PERSON><PERSON>, <PERSON><PERSON>ultHeaders};
use actix_cors::Cors;
use actix_files;
use std::env;
use std::sync::Arc;
use dotenv::dotenv;
use log::{error, info};
use env_logger::Env;

use quiickchat::middleware::auth::AuthMiddleware;
use quiickchat::middleware::security_headers::SecurityHeaders;
use quiickchat::middleware::rate_limit::{RateLimiter, RateLimitConfig};

use quiickchat::controllers;
use quiickchat::routes;
use quiickchat::services::{
    MongoDBService, TwilioService,
    UserService, UserSettingsService, KeysService, StatusService,
    ContactService, WebSocketService
};

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    dotenv().ok();

    // Initialize logger with custom environment
    env_logger::init_from_env(Env::default().default_filter_or("info"));

    // Always bind to 0.0.0.0 to allow external connections
    // This works both locally and in cloud environments like Render
    let host = "0.0.0.0".to_string(); // Force 0.0.0.0 for Render compatibility
    let port = env::var("PORT").unwrap_or_else(|_| "8080".to_string());
    let server_url = format!("{}:{}", host, port);


    info!("Server binding to: {}", server_url);

    // Set SERVER_URL environment variable if not already set
    if env::var("SERVER_URL").is_err() {
        let public_url = format!("http://{}:{}", host, port);
        env::set_var("SERVER_URL", public_url.clone());
        info!("SERVER_URL set to: {}", public_url);
    }

    // Validate JWT secret is provided
    if env::var("JWT_SECRET").is_err() {
        error!("JWT_SECRET environment variable is required for production");
        std::process::exit(1);
    }



  

    info!("Starting server at: {}", server_url);

    // Initialize MongoDB Service
    let mongodb_service = match MongoDBService::new().await {
        Ok(service) => {
            info!("MongoDB Service initialized successfully");
            Arc::new(service)
        }
        Err(e) => {
            error!("Failed to initialize MongoDB Service: {}", e);
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!("Failed to initialize MongoDB Service: {}", e),
            ));
        }
    };



    // We're now using CloudinaryUploader instead of CloudinaryService
    // No need to initialize CloudinaryService anymore

    // Initialize CloudinaryUploader with required environment variables
    let cloudinary_uploader = {
        // Check if required environment variables are set
        let cloud_name = match env::var("CLOUDINARY_CLOUD_NAME") {
            Ok(name) => {
                if name.is_empty() || name == "default" {
                    error!("CLOUDINARY_CLOUD_NAME environment variable is not properly set");
                    return Err(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        "CLOUDINARY_CLOUD_NAME environment variable is required",
                    ));
                }
                name
            },
            Err(_) => {
                error!("CLOUDINARY_CLOUD_NAME environment variable is not set");
                return Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    "CLOUDINARY_CLOUD_NAME environment variable is required",
                ));
            }
        };

        let api_key = match env::var("CLOUDINARY_API_KEY") {
            Ok(key) => {
                if key.is_empty() {
                    error!("CLOUDINARY_API_KEY environment variable is not properly set");
                    return Err(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        "CLOUDINARY_API_KEY environment variable is required",
                    ));
                }
                key
            },
            Err(_) => {
                error!("CLOUDINARY_API_KEY environment variable is not set");
                return Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    "CLOUDINARY_API_KEY environment variable is required",
                ));
            }
        };

        let api_secret = match env::var("CLOUDINARY_API_SECRET") {
            Ok(secret) => {
                if secret.is_empty() {
                    error!("CLOUDINARY_API_SECRET environment variable is not properly set");
                    return Err(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        "CLOUDINARY_API_SECRET environment variable is required",
                    ));
                }
                secret
            },
            Err(_) => {
                error!("CLOUDINARY_API_SECRET environment variable is not set");
                return Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    "CLOUDINARY_API_SECRET environment variable is required",
                ));
            }
        };

        info!("Initializing CloudinaryUploader with cloud_name: {}", cloud_name);
        let uploader = Arc::new(quiickchat::services::CloudinaryUploader::new(
            cloud_name,
            api_key,
            api_secret,
            "quiickchat".to_string(), // Use the existing quiickchat folder
        ));

        // Check if Cloudinary credentials are valid
        match uploader.check_credentials().await {
            Ok(true) => info!("Cloudinary credentials are valid"),
            Ok(false) => {
                error!("Cloudinary credentials are invalid");
                return Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    "Cloudinary credentials are invalid",
                ));
            },
            Err(e) => {
                error!("Failed to check Cloudinary credentials: {}", e);
                return Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Failed to check Cloudinary credentials: {}", e),
                ));
            },
        }

        uploader
    };

    // Initialize Twilio Service
    let twilio_service = Arc::new(TwilioService::new());
    info!("Twilio Service initialized successfully");

    // Initialize User Service
    let user_service = Arc::new(UserService::new(mongodb_service.clone()));
    info!("User Service initialized successfully");

    // Initialize User Settings Service
    let user_settings_service = Arc::new(UserSettingsService::new(mongodb_service.clone()));
    info!("User Settings Service initialized successfully");

    // Initialize Keys Service
    let keys_service = Arc::new(KeysService::new(mongodb_service.clone()));
    info!("Keys Service initialized successfully");

    // Initialize Status Service
    let status_service = Arc::new(StatusService::new(mongodb_service.clone()));
    info!("Status Service initialized successfully");

    // Initialize ContactService
    let contact_service = Arc::new(ContactService::new(mongodb_service.clone()));
    info!("Contact Service initialized successfully");

    // Initialize WebSocketService
    let websocket_service = Arc::new(WebSocketService::new(contact_service.clone()));
    info!("WebSocket Service initialized successfully");

    // Create UsersControllerState
    let users_controller_state = web::Data::new(controllers::users_controller::UsersControllerState {
        user_service: user_service.clone(),
        twilio_service: twilio_service.clone(),
        cloudinary_uploader: cloudinary_uploader.clone(),
        user_settings_service: user_settings_service.clone(),
        keys_service: keys_service.clone(),
    });

    // Create KeysControllerState
    let keys_controller_state = web::Data::new(controllers::keys_controller::KeysControllerState {
        keys_service: keys_service.clone(),
    });

    // Create StatusControllerState
    let status_controller_state = web::Data::new(controllers::status_controller::StatusControllerState {
        status_service: status_service.clone(),
        cloudinary_uploader: cloudinary_uploader.clone(),
        user_service: user_service.clone(),
        websocket_service: websocket_service.clone(),
    });

    // Create ContactControllerState
    let contact_controller_state = web::Data::new(controllers::contact_controller::ContactControllerState {
        contact_service: contact_service.clone(),
        websocket_service: websocket_service.clone(),
    });

    // Create a simple health check handler that responds immediately
    async fn render_health_check() -> HttpResponse {
        let port = env::var("PORT").unwrap_or_else(|_| "8080".to_string());
        println!("Health check requested");

        HttpResponse::Ok()
            .append_header(("X-Port", port.clone()))
            .json(serde_json::json!({
                "status": "ok",
                "port": port,
                "server": "quiickchat",
                "listening": true,
                "timestamp": chrono::Utc::now().to_rfc3339()
            }))
    }

    HttpServer::new(move || {
        let cors = Cors::default()
            .allow_any_origin()
            .allow_any_method()
            .allow_any_header()
            .supports_credentials()
            .max_age(3600);

        // Configure rate limiting
        let rate_limit_config = RateLimitConfig {
            requests_per_minute: 120,  // 120 requests per minute
            burst_size: 30,            // Allow bursts of 30 requests
        };

        // Main app with all middleware
        App::new()
            // Add middleware in the correct order
            .wrap(Logger::new("%r %s %b %{User-Agent}i %a %T"))
            .wrap(cors)
            // Enable security headers
            .wrap(SecurityHeaders)
            // Enable rate limiting
            .wrap(RateLimiter::new(rate_limit_config))
            // Temporarily disable input validation
            // .wrap(InputValidationMiddleware)
            // Enable authentication
            .wrap(AuthMiddleware)
            // Add default headers
            .wrap(
                DefaultHeaders::new()
                    .add(("X-Content-Type-Options", "nosniff"))
                    .add(("X-XSS-Protection", "1; mode=block"))
            )
            // Add health check routes without middleware for Render to detect
            .service(
                web::scope("/render-health")
                    .wrap(actix_web::middleware::NormalizePath::new(
                        actix_web::middleware::TrailingSlash::Trim
                    ))
                    .route("", web::get().to(render_health_check))
                    .route("/", web::get().to(render_health_check))
            )
            .app_data(users_controller_state.clone())
            .app_data(keys_controller_state.clone())
            .app_data(status_controller_state.clone())
            .app_data(contact_controller_state.clone())
            .configure(routes::configure_routes)
            // Serve static files
            .service(actix_files::Files::new("/profiles", "static/profiles").show_files_listing())

            // Root endpoint for basic server check - using the health check function
            .route("/", web::get().to(render_health_check))
            // Direct health check endpoint for Render
            .route("/health", web::get().to(render_health_check))
    })
    // Explicitly bind to the server URL for Render to detect
    .bind(&server_url)?
    .workers(1) // Use single worker for faster startup
    .shutdown_timeout(1) // Fast shutdown
    .run()
    .await
}
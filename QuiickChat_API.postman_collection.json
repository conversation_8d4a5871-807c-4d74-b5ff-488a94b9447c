{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "QuiickChat API", "description": "Complete API collection for QuiickChat application with all endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string", "description": "Base URL without /api/v1 - this will be added automatically"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "phone", "value": "+2347049670618", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.verification_code) {", "        pm.environment.set('verification_code', response.data.verification_code);", "        console.log('Verification code saved:', response.data.verification_code);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Register a new user with phone number. Returns verification code in development mode."}}, {"name": "Verify Registration", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.auth_tokens) {", "        pm.environment.set('access_token', response.data.auth_tokens.access_token);", "        pm.environment.set('refresh_token', response.data.auth_tokens.refresh_token);", "        console.log('<PERSON><PERSON><PERSON> saved successfully');", "    }", "    if (response.data && response.data.user && response.data.user.id) {", "        pm.environment.set('user_id', response.data.user.id);", "        console.log('User ID saved:', response.data.user.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\",\n    \"code\": \"{{verification_code}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify"]}, "description": "Verify registration with phone number and verification code. Returns user data and auth tokens."}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.verification_code) {", "        pm.environment.set('verification_code', response.data.verification_code);", "        console.log('Login verification code saved:', response.data.verification_code);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Login existing user with phone number. Returns verification code in development mode."}}, {"name": "<PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.auth_tokens) {", "        pm.environment.set('access_token', response.data.auth_tokens.access_token);", "        pm.environment.set('refresh_token', response.data.auth_tokens.refresh_token);", "        console.log('Login tokens saved successfully');", "    }", "    if (response.data && response.data.user && response.data.user.id) {", "        pm.environment.set('user_id', response.data.user.id);", "        console.log('User ID saved:', response.data.user.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\",\n    \"code\": \"{{verification_code}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify-login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify-login"]}, "description": "Verify login with phone number and verification code. Returns user data and auth tokens."}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        console.log('New access token saved');", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{refresh_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh-token", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh-token"]}, "description": "Refresh access token using refresh token."}}, {"name": "Resend Verification Code", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.verification_code) {", "        pm.environment.set('verification_code', response.data.verification_code);", "        console.log('Resent verification code saved:', response.data.verification_code);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/resend-code", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "resend-code"]}, "description": "Resend verification code to phone number."}}], "description": "Authentication endpoints for user registration, login, and token management."}, {"name": "👤 User Management", "item": [{"name": "Initialize User (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/init?phone={{phone}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "init"], "query": [{"key": "phone", "value": "{{phone}}"}]}, "description": "Initialize user data and get comprehensive user information. No authentication required."}}, {"name": "Get Current User (Me)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/me?phone={{phone}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "me"], "query": [{"key": "phone", "value": "{{phone}}"}]}, "description": "Get current user information including profile, settings, and all user data."}}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"john_doe\",\n    \"bio\": \"Hello, I'm using QuiickChat!\",\n    \"status\": \"Available\",\n    \"address\": \"Lagos, Nigeria\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Update user profile information."}}, {"name": "Upload Profile Picture", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "profile_picture", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/users/upload-profile-picture", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "upload-profile-picture"]}, "description": "Upload a profile picture for the user."}}, {"name": "Get Profile Picture", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/profile/picture/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile", "picture", "{{user_id}}"]}, "description": "Get profile picture URL for a specific user."}}, {"name": "Delete User Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/delete-account", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "delete-account"]}, "description": "Delete the current user's account permanently."}}], "description": "User management endpoints for profile operations."}, {"name": "⚙️ User Settings", "item": [{"name": "Get Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/settings?phone={{phone}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings"], "query": [{"key": "phone", "value": "{{phone}}"}]}, "description": "Get all user settings."}}, {"name": "Update Notification Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"message_notifications\": true,\n    \"call_notifications\": true,\n    \"status_notifications\": false,\n    \"sound_enabled\": true,\n    \"vibration_enabled\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/notifications?phone={{phone}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "notifications"], "query": [{"key": "phone", "value": "{{phone}}"}]}, "description": "Update notification preferences."}}, {"name": "Update Privacy Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"profile_photo_visibility\": \"contacts\",\n    \"last_seen_visibility\": \"contacts\",\n    \"status_visibility\": \"contacts\",\n    \"read_receipts\": true,\n    \"typing_indicators\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/privacy?phone={{phone}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "privacy"], "query": [{"key": "phone", "value": "{{phone}}"}]}, "description": "Update privacy settings."}}, {"name": "Update Appearance Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"theme\": \"dark\",\n    \"font_size\": \"medium\",\n    \"chat_wallpaper\": \"default\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/appearance?phone={{phone}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "appearance"], "query": [{"key": "phone", "value": "{{phone}}"}]}, "description": "Update appearance preferences."}}, {"name": "Update Language", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"language\": \"en\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/language?phone={{phone}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "language"], "query": [{"key": "phone", "value": "{{phone}}"}]}, "description": "Update language preference."}}], "description": "User settings management endpoints."}, {"name": "📱 Status Management", "item": [{"name": "Create Text Status", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"content\": \"Hello from <PERSON><PERSON>ick<PERSON><PERSON>! 👋\",\n    \"content_type\": \"text\",\n    \"privacy\": \"contacts\",\n    \"background_color\": \"#4CAF50\",\n    \"text_color\": \"#FFFFFF\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "status"]}, "description": "Create a text-only status update."}}, {"name": "Upload Status with Media", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": []}, {"key": "content", "value": "Check out this photo!", "type": "text"}, {"key": "content_type", "value": "image", "type": "text"}, {"key": "privacy", "value": "contacts", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/status/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "upload"]}, "description": "Upload a status with image or video media."}}, {"name": "Get Status Feed", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/feed", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "feed"]}, "description": "Get status updates from contacts (status feed)."}}, {"name": "Get My Statuses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/my", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "my"]}, "description": "Get current user's status updates."}}, {"name": "Get User Statuses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "user", "{{user_id}}"]}, "description": "Get status updates for a specific user."}}, {"name": "View Status", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status_id\": \"status_id_here\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/view", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "view"]}, "description": "Mark a status as viewed (for view tracking)."}}, {"name": "Get Status Views", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/status_id_here/views", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "status_id_here", "views"]}, "description": "Get list of users who viewed a specific status."}}, {"name": "Delete Status", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/status_id_here", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "status_id_here"]}, "description": "Delete a specific status update."}}], "description": "Status management endpoints for creating and managing WhatsApp-like status updates."}, {"name": "🔒 Status Privacy", "item": [{"name": "Get Status Privacy Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/privacy", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "privacy"]}, "description": "Get current status privacy settings."}}, {"name": "Update Status Privacy Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"default_privacy\": \"contacts\",\n    \"hide_from\": [],\n    \"show_only_to\": []\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/privacy", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "privacy"]}, "description": "Update status privacy settings."}}], "description": "Status privacy management endpoints."}, {"name": "🔐 Encryption Keys", "item": [{"name": "Generate Encryption Keys", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"device_id\": \"device_123\",\n    \"registration_id\": 12345\n}"}, "url": {"raw": "{{base_url}}/api/v1/keys/generate", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "generate"]}, "description": "Generate new encryption keys for the user."}}, {"name": "Get Pre-Key Bundle", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/keys/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "{{user_id}}"]}, "description": "Get pre-key bundle for a specific user."}}, {"name": "Rotate Encryption Keys", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"device_id\": \"device_123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/keys/rotate", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "rotate"]}, "description": "Rotate encryption keys for security."}}, {"name": "Revoke Encryption Keys", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"device_id\": \"device_123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/keys/revoke", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "revoke"]}, "description": "Revoke encryption keys."}}, {"name": "Get Session Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/keys/session/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "session", "{{user_id}}"]}, "description": "Get encryption session status with a specific user."}}], "description": "Encryption key management endpoints for secure messaging."}, {"name": "🔧 Debug & Utilities", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check if the API service is running."}}, {"name": "Debug Phone Lookup (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/debug-phone?phone={{phone}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "debug-phone"], "query": [{"key": "phone", "value": "{{phone}}"}]}, "description": "Debug endpoint to test phone number lookup in database. No authentication required."}}, {"name": "Debug <PERSON><PERSON><PERSON> (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/debug-twilio", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "debug-twilio"]}, "description": "Debug endpoint to test Twilio credentials and connectivity. No authentication required."}}], "description": "Debug and utility endpoints for testing and troubleshooting."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set base URL if not already set", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'http://localhost:8080');", "}", "", "// Log current environment variables for debugging", "console.log('Current Environment Variables:');", "console.log('base_url:', pm.environment.get('base_url'));", "console.log('access_token:', pm.environment.get('access_token') ? 'SET' : 'NOT SET');", "console.log('refresh_token:', pm.environment.get('refresh_token') ? 'SET' : 'NOT SET');", "console.log('user_id:', pm.environment.get('user_id'));", "console.log('phone:', pm.environment.get('phone'));", "console.log('verification_code:', pm.environment.get('verification_code'));"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script for all requests", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has success field', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('success');", "});", "", "pm.test('Response has message field', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('message');", "});", "", "// Log response for debugging", "console.log('Response Status:', pm.response.code);", "console.log('Response Body:', pm.response.text());"]}}]}
#!/usr/bin/env ts-node

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import { EC2Service } from '@/services/EC2Service';
import config from '@/config';
import logger from '@/utils/logger';
import { CliCommand, CliOptions } from '@/types';

const program = new Command();

// CLI configuration
program
  .name('ec2-cli')
  .description('QuiickChat EC2 Instance Manager CLI')
  .version('1.0.0');

// Status command
program
  .command('status')
  .description('Get EC2 instance status')
  .option('-i, --include-ips', 'Include IP addresses in output')
  .option('-v, --verbose', 'Verbose output')
  .action(async (options: CliOptions) => {
    await handleCommand('status', options);
  });

// Start command
program
  .command('start')
  .description('Start EC2 instance')
  .option('-w, --wait', 'Wait for instance to be running')
  .option('-t, --timeout <seconds>', 'Timeout for wait operation (default: 300)', '300')
  .option('-v, --verbose', 'Verbose output')
  .action(async (options: CliOptions) => {
    await handleCommand('start', options);
  });

// Stop command
program
  .command('stop')
  .description('Stop EC2 instance')
  .option('-w, --wait', 'Wait for instance to be stopped')
  .option('-t, --timeout <seconds>', 'Timeout for wait operation (default: 300)', '300')
  .option('-v, --verbose', 'Verbose output')
  .action(async (options: CliOptions) => {
    await handleCommand('stop', options);
  });

// Restart command
program
  .command('restart')
  .description('Restart EC2 instance (stop then start)')
  .option('-w, --wait', 'Wait for restart to complete')
  .option('-t, --timeout <seconds>', 'Timeout for wait operation (default: 600)', '600')
  .option('-v, --verbose', 'Verbose output')
  .action(async (options: CliOptions) => {
    await handleCommand('restart', options);
  });

/**
 * Handle CLI commands
 */
async function handleCommand(command: CliCommand, options: CliOptions): Promise<void> {
  const ec2Service = new EC2Service();
  
  try {
    console.log(chalk.blue('🚀 QuiickChat EC2 Manager CLI\n'));
    
    if (options.verbose) {
      console.log(chalk.gray(`Region: ${config.aws.region}`));
      console.log(chalk.gray(`Instance: ${config.aws.instanceId.substring(0, 10)}...${config.aws.instanceId.slice(-4)}\n`));
    }

    switch (command) {
      case 'status':
        await handleStatus(ec2Service, options);
        break;
      case 'start':
        await handleStart(ec2Service, options);
        break;
      case 'stop':
        await handleStop(ec2Service, options);
        break;
      case 'restart':
        await handleRestart(ec2Service, options);
        break;
    }
  } catch (error) {
    console.error(chalk.red('❌ Error:'), error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  }
}

/**
 * Handle status command
 */
async function handleStatus(ec2Service: EC2Service, options: CliOptions): Promise<void> {
  const spinner = ora('Getting instance status...').start();
  
  try {
    const status = await ec2Service.getRedactedInstanceInfo(options.includeIps);
    spinner.stop();
    
    console.log(chalk.green('📊 Instance Status:'));
    console.log(`   State: ${getStateColor(status.state)} (${status.stateCode})`);
    console.log(`   Type: ${chalk.cyan(status.instanceType)}`);
    console.log(`   Zone: ${chalk.cyan(status.availabilityZone)}`);
    
    if (status.launchTime) {
      const launchTime = new Date(status.launchTime);
      console.log(`   Launched: ${chalk.cyan(launchTime.toLocaleString())}`);
    }
    
    if (options.includeIps) {
      if (status.publicIp) {
        console.log(`   Public IP: ${chalk.cyan(status.publicIp)}`);
      }
      if (status.privateIp) {
        console.log(`   Private IP: ${chalk.cyan(status.privateIp)}`);
      }
    } else {
      console.log(`   Has Public IP: ${status.hasPublicIp ? chalk.green('Yes') : chalk.red('No')}`);
      console.log(`   Has Private IP: ${status.hasPrivateIp ? chalk.green('Yes') : chalk.red('No')}`);
    }
    
    console.log(`   Last Updated: ${chalk.gray(new Date(status.timestamp).toLocaleString())}`);
  } catch (error) {
    spinner.fail('Failed to get instance status');
    throw error;
  }
}

/**
 * Handle start command
 */
async function handleStart(ec2Service: EC2Service, options: CliOptions): Promise<void> {
  const spinner = ora('Starting instance...').start();
  
  try {
    const result = await ec2Service.startInstance();
    
    if (result.alreadyInState) {
      spinner.succeed(chalk.green('✅ Instance is already running!'));
      return;
    }
    
    spinner.succeed(chalk.green('✅ Start request sent successfully!'));
    console.log(`   Previous State: ${chalk.yellow(result.previousState || 'unknown')}`);
    console.log(`   Current State: ${getStateColor(result.currentState)}`);
    console.log(`   Estimated Time: ${chalk.cyan(result.estimatedTime || 'unknown')}`);
    
    if (options.wait) {
      const waitSpinner = ora('Waiting for instance to be running...').start();
      const timeout = (options.timeout || 300) * 1000;
      
      const success = await ec2Service.waitForState('running', timeout);
      
      if (success) {
        waitSpinner.succeed(chalk.green('✅ Instance is now running!'));
        
        // Show final status
        const finalStatus = await ec2Service.getRedactedInstanceInfo(true);
        if (finalStatus.publicIp) {
          console.log(`   Public IP: ${chalk.cyan(finalStatus.publicIp)}`);
        }
      } else {
        waitSpinner.fail(chalk.yellow('⏰ Timeout waiting for instance to start'));
        console.log(chalk.gray('   Use "ec2-cli status" to check current state'));
      }
    }
  } catch (error) {
    spinner.fail('Failed to start instance');
    throw error;
  }
}

/**
 * Handle stop command
 */
async function handleStop(ec2Service: EC2Service, options: CliOptions): Promise<void> {
  const spinner = ora('Stopping instance...').start();
  
  try {
    const result = await ec2Service.stopInstance();
    
    if (result.alreadyInState) {
      spinner.succeed(chalk.green('✅ Instance is already stopped!'));
      return;
    }
    
    spinner.succeed(chalk.green('✅ Stop request sent successfully!'));
    console.log(`   Previous State: ${chalk.yellow(result.previousState || 'unknown')}`);
    console.log(`   Current State: ${getStateColor(result.currentState)}`);
    console.log(`   Estimated Time: ${chalk.cyan(result.estimatedTime || 'unknown')}`);
    
    if (options.wait) {
      const waitSpinner = ora('Waiting for instance to be stopped...').start();
      const timeout = (options.timeout || 300) * 1000;
      
      const success = await ec2Service.waitForState('stopped', timeout);
      
      if (success) {
        waitSpinner.succeed(chalk.green('✅ Instance is now stopped!'));
      } else {
        waitSpinner.fail(chalk.yellow('⏰ Timeout waiting for instance to stop'));
        console.log(chalk.gray('   Use "ec2-cli status" to check current state'));
      }
    }
  } catch (error) {
    spinner.fail('Failed to stop instance');
    throw error;
  }
}

/**
 * Handle restart command
 */
async function handleRestart(ec2Service: EC2Service, options: CliOptions): Promise<void> {
  console.log(chalk.blue('🔄 Restarting instance...\n'));
  
  // First stop the instance
  await handleStop(ec2Service, { ...options, wait: true });
  
  console.log(); // Empty line
  
  // Then start the instance
  await handleStart(ec2Service, { ...options, wait: true });
}

/**
 * Get colored state text
 */
function getStateColor(state: string): string {
  switch (state.toLowerCase()) {
    case 'running':
      return chalk.green(state.toUpperCase());
    case 'stopped':
      return chalk.red(state.toUpperCase());
    case 'pending':
    case 'stopping':
      return chalk.yellow(state.toUpperCase());
    case 'terminated':
      return chalk.gray(state.toUpperCase());
    default:
      return chalk.white(state.toUpperCase());
  }
}

// Parse command line arguments
program.parse();

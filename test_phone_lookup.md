# Phone Number Lookup Test

Based on your database record, the phone number is stored as: `"+23470496706180"`

## Test the Login API

### 1. Test with the exact phone number from database:

**Request:**
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+23470496706180"
  }'
```

### 2. Test without the + prefix:

**Request:**
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "23470496706180"
  }'
```

### 3. Test with spaces (should be trimmed):

**Request:**
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "phone": " +23470496706180 "
  }'
```

## Expected Successful Response:

```json
{
  "success": true,
  "message": "Verification code sent",
  "data": {
    "phone": "+23470496706180"
  },
  "timestamp": 1640995200000
}
```

## If Still Getting "User not found" Error:

### Check Server Logs
The server should log:
```
Logging in user with formatted phone: '+23470496706180'
Searching for user with phone: '+23470496706180'
```

### Verify Database Connection
Test if you can get the user by phone directly:

**Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/users/details?phone=%2B23470496706180"
```

Note: `%2B` is URL-encoded `+`

## Troubleshooting Steps:

1. **Check the exact phone format you're sending**
2. **Check server logs for the formatted phone number**
3. **Verify the user exists and is verified in the database**
4. **Test the user details endpoint first**

## Postman Test:

In your Postman collection:

1. Update the `phone` variable to: `+23470496706180`
2. Run the "Login" request
3. Check the response

## Common Issues:

- **URL Encoding**: If testing via browser/Postman, the `+` might need to be URL encoded as `%2B`
- **Phone Format**: Make sure you're sending exactly `"+23470496706180"`
- **User Verification**: Ensure `is_verified: true` in the database
- **Database Connection**: Verify MongoDB is connected and accessible

## Debug the Issue:

Add this temporary debug endpoint to test phone lookup directly. Add to your users controller:

```rust
// Temporary debug endpoint
pub async fn debug_phone_lookup(
    data: web::Data<UsersControllerState>,
    query: web::Query<std::collections::HashMap<String, String>>,
) -> Result<HttpResponse, AppError> {
    if let Some(phone) = query.get("phone") {
        log::info!("Debug: Looking up phone: '{}'", phone);
        
        match data.user_service.get_user_by_phone(phone).await {
            Ok(user) => {
                log::info!("Debug: Found user with ID: {:?}", user.id);
                Ok(HttpResponse::Ok().json(json!({
                    "success": true,
                    "message": "User found",
                    "data": {
                        "user_id": user.id,
                        "phone": user.phone,
                        "is_verified": user.is_verified
                    }
                })))
            },
            Err(e) => {
                log::error!("Debug: User lookup failed: {}", e);
                Ok(HttpResponse::Ok().json(json!({
                    "success": false,
                    "message": format!("User lookup failed: {}", e),
                    "data": {
                        "searched_phone": phone
                    }
                })))
            }
        }
    } else {
        Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "message": "Phone parameter required"
        })))
    }
}
```

Then test: `GET /debug-phone?phone=+23470496706180`

{"info": {"_postman_id": "merged-12345678-1234-1234-1234-123456789012", "name": "QuiickChat API - Merged Collection", "description": "Complete merged API collection for QuiickChat with all endpoints, debug utilities, and comprehensive testing scripts.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12345678"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set base URL if not already set", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'http://localhost:8080');", "}", "", "// Auto-set phone number if not already set", "if (!pm.environment.get('phone')) {", "    pm.environment.set('phone', '+2347049670618');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script for all requests", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has valid JSON structure', function () {", "    try {", "        const response = pm.response.json();", "        pm.expect(response).to.have.property('success');", "    } catch (e) {", "        // Skip for non-JSON responses", "    }", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string", "description": "Base URL for the QuiickChat API"}, {"key": "access_token", "value": "", "type": "string", "description": "JWT access token for authentication"}, {"key": "refresh_token", "value": "", "type": "string", "description": "JWT refresh token for token renewal"}, {"key": "user_id", "value": "", "type": "string", "description": "Current user ID"}, {"key": "phone", "value": "+2347049670618", "type": "string", "description": "Phone number for testing (Nigerian format)"}, {"key": "verification_code", "value": "123456", "type": "string", "description": "SMS verification code (auto-populated in development)"}, {"key": "status_id", "value": "", "type": "string", "description": "Status ID for testing status operations"}, {"key": "contact_user_id", "value": "", "type": "string", "description": "Contact user ID for testing"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        if (response.data.verification_code) {", "            pm.environment.set('verification_code', response.data.verification_code);", "            console.log('✅ Verification code saved:', response.data.verification_code);", "        }", "        if (response.data.user_id) {", "            pm.environment.set('user_id', response.data.user_id);", "            console.log('✅ User ID saved:', response.data.user_id);", "        }", "        console.log('✅ User registered successfully');", "    }", "} else {", "    console.log('❌ Registration failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Register a new user with phone number. Sends SMS verification code. In development, the verification code is included in the response."}}, {"name": "Verify Registration", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        if (response.data.user && response.data.user.id) {", "            pm.environment.set('user_id', response.data.user.id);", "        }", "        console.log('✅ Registration verified, tokens saved');", "        console.log('Access Token:', response.data.access_token.substring(0, 20) + '...');", "    }", "} else {", "    console.log('❌ Verification failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\",\n    \"code\": \"{{verification_code}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify"]}, "description": "Verify registration with SMS code. Returns access and refresh tokens."}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        if (response.data.verification_code) {", "            pm.environment.set('verification_code', response.data.verification_code);", "            console.log('✅ Verification code saved:', response.data.verification_code);", "        }", "        if (response.data.user_id) {", "            pm.environment.set('user_id', response.data.user_id);", "        }", "        console.log('✅ Login initiated, verification code sent');", "    }", "} else {", "    console.log('❌ <PERSON><PERSON> failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Login existing user. Sends SMS verification code."}}, {"name": "<PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        if (response.data.user && response.data.user.id) {", "            pm.environment.set('user_id', response.data.user.id);", "        }", "        console.log('✅ Login verified, tokens saved');", "        console.log('Access Token:', response.data.access_token.substring(0, 20) + '...');", "    }", "} else {", "    console.log('❌ Login verification failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\",\n    \"code\": \"{{verification_code}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify-login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify-login"]}, "description": "Verify login with SMS code. Returns access and refresh tokens."}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.environment.set('access_token', response.data.access_token);", "        console.log('✅ Token refreshed successfully');", "        console.log('New Access Token:', response.data.access_token.substring(0, 20) + '...');", "    }", "} else {", "    console.log('❌ Token refresh failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh-token", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh-token"]}, "description": "Refresh access token using refresh token."}}, {"name": "Resend Verification Code", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.verification_code) {", "        pm.environment.set('verification_code', response.data.verification_code);", "        console.log('✅ Verification code resent:', response.data.verification_code);", "    }", "} else {", "    console.log('❌ Failed to resend code:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/resend-code", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "resend-code"]}, "description": "Resend SMS verification code."}}], "description": "Authentication endpoints for user registration, login, and token management."}, {"name": "👤 User Management", "item": [{"name": "Initialize User (Public)", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/init", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "init"]}, "description": "Public endpoint to initialize user session and get basic info."}}, {"name": "Get Current User (Me)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        console.log('✅ User profile retrieved');", "        console.log('User ID:', response.data.id);", "        console.log('Username:', response.data.username);", "        console.log('Phone:', response.data.phone);", "    }", "} else {", "    console.log('❌ Failed to get user profile:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/me", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "me"]}, "description": "Get current user profile information."}}, {"name": "Update User Profile", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success) {", "        console.log('✅ Profile updated successfully');", "    }", "} else {", "    console.log('❌ Profile update failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"john_doe_updated\",\n    \"status\": \"Hey there! I'm using QuiickChat 👋 Updated status!\",\n    \"bio\": \"Software developer and tech enthusiast. Love building cool apps!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Update user profile information including username, status, and bio."}}, {"name": "Upload Profile Picture", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.profile_picture_url) {", "        console.log('✅ Profile picture uploaded successfully');", "        console.log('Profile Picture URL:', response.data.profile_picture_url);", "    }", "} else {", "    console.log('❌ Profile picture upload failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "profile_picture", "type": "file", "src": [], "description": "Select an image file (JPG, PNG, GIF, WebP, max 10MB)"}]}, "url": {"raw": "{{base_url}}/api/v1/users/upload-profile-picture", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "upload-profile-picture"]}, "description": "Upload profile picture. Automatically uploads to Cloudinary and returns the URL."}}, {"name": "Get Profile Picture", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/profile/picture/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile", "picture", "{{user_id}}"]}, "description": "Get profile picture for a specific user."}}, {"name": "Delete User Account", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success) {", "        console.log('✅ Account deleted successfully');", "        // Clear tokens since account is deleted", "        pm.environment.unset('access_token');", "        pm.environment.unset('refresh_token');", "        pm.environment.unset('user_id');", "    }", "} else {", "    console.log('❌ Account deletion failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/delete-account", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "delete-account"]}, "description": "Delete user account permanently. This action cannot be undone."}}], "description": "User management endpoints for profile operations and account management."}, {"name": "⚙️ User Settings", "item": [{"name": "Get Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/settings", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings"]}, "description": "Get user settings (notifications, privacy, appearance)."}}, {"name": "Update Notification Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"message_notifications\": true,\n    \"group_notifications\": true,\n    \"in_app_sounds\": true,\n    \"in_app_vibrations\": false,\n    \"show_previews\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/notifications", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "notifications"]}, "description": "Update notification settings."}}, {"name": "Update Privacy Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"last_seen\": \"contacts\",\n    \"profile_photo\": \"everyone\",\n    \"status\": \"contacts\",\n    \"read_receipts\": true,\n    \"groups\": \"contacts\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/privacy", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "privacy"]}, "description": "Update privacy settings."}}, {"name": "Update Appearance Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"theme\": \"dark\",\n    \"font_size\": \"medium\",\n    \"chat_wallpaper\": \"default\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/appearance", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "appearance"]}, "description": "Update appearance settings."}}, {"name": "Update Language", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"language\": \"en\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/settings/language", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "settings", "language"]}, "description": "Update user language preference."}}], "description": "User settings management for notifications, privacy, appearance, and language."}, {"name": "📞 Contact Management", "item": [{"name": "Upload Contacts", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success) {", "        console.log('✅ Contacts uploaded successfully');", "        console.log('Contacts processed:', response.contacts_processed);", "        console.log('Registered contacts:', response.contacts_registered);", "        console.log('Message:', response.message);", "    }", "} else {", "    console.log('❌ Contact upload failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"contacts\": [\n        {\n            \"display_name\": \"<PERSON>\",\n            \"phone_number\": \"+1234567890\"\n        },\n        {\n            \"display_name\": \"<PERSON>\",\n            \"phone_number\": \"+0987654321\"\n        },\n        {\n            \"display_name\": \"<PERSON>\",\n            \"phone_number\": \"+1122334455\"\n        },\n        {\n            \"display_name\": \"<PERSON>\",\n            \"phone_number\": \"+2347049670619\"\n        },\n        {\n            \"display_name\": \"<PERSON>\",\n            \"phone_number\": \"+2348123456789\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/contacts/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "upload"]}, "description": "Upload contacts from device. Merges with existing contacts and checks registration status. Now uses upsert logic to avoid duplicates."}}, {"name": "Get Contacts", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        console.log('✅ Contacts retrieved successfully');", "        console.log('Total contacts:', response.data.length);", "        const registeredContacts = response.data.filter(c => c.is_registered);", "        console.log('Registered contacts:', registeredContacts.length);", "        ", "        // Save a contact user ID for testing", "        if (registeredContacts.length > 0) {", "            pm.environment.set('contact_user_id', registeredContacts[0].registered_user_id);", "        }", "    }", "} else {", "    console.log('❌ Failed to get contacts:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/contacts", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts"]}, "description": "Get user's uploaded contacts with registration status and profile information."}}, {"name": "Sync Contacts", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success) {", "        console.log('✅ Contacts synced successfully');", "        console.log('Updated contacts:', response.updated_count || 'N/A');", "    }", "} else {", "    console.log('❌ Contact sync failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/contacts/sync", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "sync"]}, "description": "Re-check registration status for all contacts. Useful when contacts may have joined the platform."}}], "description": "Contact management endpoints for uploading, syncing, and retrieving contacts."}, {"name": "🌐 WebSocket & Real-time", "item": [{"name": "Connect to WebSocket", "request": {"method": "GET", "header": [{"key": "Upgrade", "value": "websocket"}, {"key": "Connection", "value": "Upgrade"}], "url": {"raw": "ws://localhost:8080/api/v1/contacts/ws?token={{access_token}}", "protocol": "ws", "host": ["localhost"], "port": "8080", "path": ["api", "v1", "contacts", "ws"], "query": [{"key": "token", "value": "{{access_token}}", "description": "JWT token for authentication"}]}, "description": "Connect to WebSocket for real-time status updates. JWT token is passed as query parameter for authentication. Use WebSocket client to test this."}}, {"name": "WebSocket Connection Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/contacts/ws/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "ws", "stats"]}, "description": "Get WebSocket connection statistics and active connections."}}], "description": "WebSocket endpoints for real-time communication and status updates."}, {"name": "📱 Status Management", "item": [{"name": "Create Text Status", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.environment.set('status_id', response.data.id);", "        console.log('✅ Text status created successfully');", "        console.log('Status ID:', response.data.id);", "        console.log('Content:', response.data.content_url);", "        console.log('Privacy:', response.data.privacy_setting);", "    }", "} else {", "    console.log('❌ Status creation failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"content\": \"Hello from Q<PERSON>ickC<PERSON>! 👋 This is a text status update with some emojis 🚀✨\",\n    \"content_type\": \"text\",\n    \"privacy_setting\": \"contacts\",\n    \"background_color\": \"#4CAF50\",\n    \"text_color\": \"#FFFFFF\",\n    \"font_style\": \"bold\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "status"]}, "description": "Create a text-only status update with styling options. Automatically broadcasts to WebSocket contacts."}}, {"name": "Upload Status with Media", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.environment.set('status_id', response.data.id);", "        console.log('✅ Media status created successfully');", "        console.log('Status ID:', response.data.id);", "        console.log('Media URL:', response.data.content_url);", "        console.log('Content Type:', response.data.content_type);", "    }", "} else {", "    console.log('❌ Media status creation failed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": [], "description": "Select a media file (Image: JPG, PNG, GIF, WebP | Video: MP4, AVI, MOV, WebM | Audio: MP3, WAV, AAC, OGG)"}, {"key": "status_data", "value": "{\n    \"caption\": \"Check out this amazing content! 📸🎬🎵\",\n    \"privacy_setting\": \"contacts\",\n    \"background_color\": \"#2196F3\"\n}", "type": "text", "description": "Status metadata as JSON string"}]}, "url": {"raw": "{{base_url}}/api/v1/status/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "upload"]}, "description": "Upload a status with media (image, video, or audio). Backend automatically uploads to Cloudinary and saves the URL. Broadcasts to WebSocket contacts."}}, {"name": "Get Status Feed", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        console.log('✅ Status feed retrieved successfully');", "        console.log('Total statuses:', response.data.length);", "        response.data.forEach((status, index) => {", "            console.log(`Status ${index + 1}: ${status.content_type} by ${status.user_id}`);", "        });", "    }", "} else {", "    console.log('❌ Failed to get status feed:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/feed", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "feed"]}, "description": "Get status updates from contacts (status feed). Shows statuses from contacts based on privacy settings."}}, {"name": "Get My Statuses", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        console.log('✅ My statuses retrieved successfully');", "        console.log('Total my statuses:', response.data.length);", "        if (response.data.length > 0) {", "            pm.environment.set('status_id', response.data[0].id);", "            console.log('Latest status ID saved:', response.data[0].id);", "        }", "    }", "} else {", "    console.log('❌ Failed to get my statuses:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/my", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "my"]}, "description": "Get current user's status updates."}}, {"name": "Get User Statuses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/user/{{contact_user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "user", "{{contact_user_id}}"]}, "description": "Get status updates for a specific user (contact)."}}, {"name": "View Status", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success) {", "        console.log('✅ Status viewed successfully');", "        console.log('Viewed at:', response.data.viewed_at);", "    }", "} else {", "    console.log('❌ Failed to view status:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status_id\": \"{{status_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/view", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "view"]}, "description": "Mark a status as viewed (for read receipts and view tracking)."}}, {"name": "Get Status Views", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/{{status_id}}/views", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "{{status_id}}", "views"]}, "description": "Get who viewed a specific status (for status owner)."}}, {"name": "Delete Status", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success) {", "        console.log('✅ Status deleted successfully');", "        console.log('Deleted at:', response.data.deleted_at);", "    }", "} else {", "    console.log('❌ Failed to delete status:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/{{status_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "{{status_id}}"]}, "description": "Delete a status update. Broadcasts deletion to WebSocket contacts."}}], "description": "Status management endpoints for creating, viewing, and managing status updates."}, {"name": "🔒 Status Privacy", "item": [{"name": "Get Status Privacy Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/status/privacy", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "privacy"]}, "description": "Get status privacy settings including default privacy, allowed/blocked contacts."}}, {"name": "Update Status Privacy Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"default_privacy\": \"contacts\",\n    \"always_allow\": [],\n    \"always_block\": [],\n    \"read_receipts_enabled\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/privacy", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "privacy"]}, "description": "Update status privacy settings. Options: everyone, contacts, contacts_except, only_share."}}], "description": "Status privacy settings management."}, {"name": "🔐 Encryption Keys", "item": [{"name": "Generate Encryption Keys", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"key_type\": \"ed25519\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/keys/generate", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "generate"]}, "description": "Generate new encryption keys for secure messaging."}}, {"name": "Rotate Encryption Keys", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/keys/rotate", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "rotate"]}, "description": "Rotate encryption keys for enhanced security."}}, {"name": "Revoke Encryption Keys", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/keys/revoke", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "revoke"]}, "description": "Revoke encryption keys."}}, {"name": "Get Pre-Key Bundle", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/keys/{{contact_user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "{{contact_user_id}}"]}, "description": "Get pre-key bundle for a specific user."}}, {"name": "Get Session Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/keys/session/{{contact_user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "keys", "session", "{{contact_user_id}}"]}, "description": "Get session status with a specific user."}}], "description": "Encryption key management for secure messaging."}, {"name": "🔧 Debug & Testing", "item": [{"name": "Phone Number Lookup", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/debug/phone-lookup/{{phone}}", "host": ["{{base_url}}"], "path": ["api", "v1", "debug", "phone-lookup", "{{phone}}"]}, "description": "Debug endpoint to lookup phone number information and validation."}}, {"name": "Test Twilio <PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\",\n    \"message\": \"Test SMS from QuiickChat API 📱\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/debug/test-sms", "host": ["{{base_url}}"], "path": ["api", "v1", "debug", "test-sms"]}, "description": "Debug endpoint to test Twilio SMS functionality."}}, {"name": "Test Cloudinary Upload", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "test_file", "type": "file", "src": [], "description": "Select any image file to test Cloudinary upload"}]}, "url": {"raw": "{{base_url}}/api/v1/debug/test-cloudinary", "host": ["{{base_url}}"], "path": ["api", "v1", "debug", "test-cloudinary"]}, "description": "Debug endpoint to test Cloudinary file upload functionality."}}, {"name": "Database Connection Test", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/debug/db-test", "host": ["{{base_url}}"], "path": ["api", "v1", "debug", "db-test"]}, "description": "Debug endpoint to test MongoDB database connection."}}], "description": "Debug and testing utilities for development and troubleshooting."}, {"name": "🏥 Health & Monitoring", "item": [{"name": "Health Check", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Health check endpoint for monitoring and deployment platforms."}}, {"name": "Root Endpoint", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Root endpoint for basic server check."}}, {"name": "API Version Info", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1", "host": ["{{base_url}}"], "path": ["api", "v1"]}, "description": "Get API version and basic information."}}], "description": "Health check and monitoring endpoints for system status."}]}
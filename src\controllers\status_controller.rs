use actix_web::{web, HttpResponse, HttpRequest};
use actix_multipart::Multipart;
use chrono::Utc;
use futures::StreamExt;
use std::sync::Arc;
use serde::{Deserialize, Serialize};

use crate::errors::AppError;
use crate::models::{
    ApiResponse, CreateStatusRequest, UpdateStatusPrivacyRequest, ViewStatusRequest,
    StatusContentType
};
use crate::services::{StatusService, CloudinaryUploader, UserService, WebSocketService};
use crate::controllers::users_controller::get_user_id_with_fallbacks;

// Query parameters for status endpoints
#[derive(Debug, Serialize, Deserialize)]
pub struct StatusQuery {
    pub user_id: Option<String>,
    pub contact_ids: Option<String>, 
}

// Shared state for the controller
pub struct StatusControllerState {
    pub status_service: Arc<StatusService>,
    pub cloudinary_uploader: Arc<CloudinaryUploader>,
    pub user_service: Arc<UserService>,
    pub websocket_service: Arc<WebSocketService>,
}

// Create a new status
pub async fn create_status(
    data: web::Data<StatusControllerState>,
    status_data: web::Json<CreateStatusRequest>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {

    let user_id = get_user_id_with_fallbacks(&req, None, None, None, &data.user_service).await?;

    let status = data.status_service.create_status(&user_id, status_data.into_inner()).await?;

    // Broadcast status update to contacts via WebSocket with privacy awareness
    let status_message = data.status_service.create_status_update_message(&status, "status_created");
    data.websocket_service.broadcast_status_with_privacy(
        &user_id,
        status_message,
        status.privacy_setting.clone(),
        &status.allowed_contacts,
        &status.blocked_contacts
    ).await;

    let response = ApiResponse {
        success: true,
        message: "Status created successfully".to_string(),
        data: Some(status),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Upload status media (image/video)
pub async fn upload_status_media(
    data: web::Data<StatusControllerState>,
    mut payload: Multipart,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {


    let user_id = get_user_id_with_fallbacks(&req, None, None, None, &data.user_service).await?;
    let temp_storage = crate::services::TempStorageService::new();
    let mut media_path: Option<(std::path::PathBuf, Option<mime::Mime>)> = None;
    let mut status_request: Option<CreateStatusRequest> = None;

    let mut has_items = false;

    let temp_dir = "temp/uploads";
    if !std::path::Path::new(temp_dir).exists() {
        std::fs::create_dir_all(temp_dir)
            .map_err(|e| AppError::InternalError(format!("Failed to create temp directory: {}", e)))?;
    }

    while let Some(item) = payload.next().await {
        has_items = true;
        let mut field = item.map_err(|e| AppError::ValidationError(format!("Failed to read multipart field: {}", e)))?;
        
        let content_disposition = field.content_disposition();
        let field_name = content_disposition.get_name().unwrap_or("unknown");

        match field_name {
            "media" => {

                let filename = content_disposition.get_filename()
                    .ok_or_else(|| AppError::ValidationError("Media filename is required".to_string()))?;
                
                log::info!("Processing media file: {}", filename);
                
                let content_type = field.content_type().cloned();
                log::info!("Media content type: {:?}", content_type);

                let is_valid_media = if let Some(ref ct) = content_type {
                    ct.type_() == mime::IMAGE || ct.type_() == mime::VIDEO || ct.type_() == mime::AUDIO
                } else {
                    false
                };

                if !is_valid_media {
                    return Err(AppError::ValidationError("Only image, video, and audio files are allowed".to_string()));
                }

                let (temp_file_path, _) = temp_storage.store_file(field).await?;
                log::info!("Media saved to temp file: {:?}", temp_file_path);
                
                media_path = Some((temp_file_path, content_type));
            },
            "status_data" => {

                let mut bytes = web::BytesMut::new();
                while let Some(chunk) = field.next().await {
                    let chunk = chunk.map_err(|e| AppError::ValidationError(format!("Failed to read status data: {}", e)))?;
                    bytes.extend_from_slice(&chunk);
                }
                
                let status_data_str = String::from_utf8(bytes.to_vec())
                    .map_err(|e| AppError::ValidationError(format!("Invalid UTF-8 in status data: {}", e)))?;
                
                log::info!("Received status data: {}", status_data_str);
                
                status_request = Some(serde_json::from_str(&status_data_str)
                    .map_err(|e| AppError::ValidationError(format!("Invalid JSON in status data: {}", e)))?);
            },
            _ => {
                log::warn!("Unknown field: {}", field_name);
                // Skip unknown fields
                while let Some(_chunk) = field.next().await {

                }
            }
        }
    }

    if !has_items {
        return Err(AppError::ValidationError("No multipart data received".to_string()));
    }


    let (temp_file_path, content_type) = media_path
        .ok_or_else(|| AppError::ValidationError("Media file is required".to_string()))?;
    
    let mut status_req = status_request
        .ok_or_else(|| AppError::ValidationError("Status data is required".to_string()))?;

    log::info!("=== UPLOADING TO CLOUDINARY ===");

    // Upload to Cloudinary
    let upload_result = data.cloudinary_uploader.upload_file_from_path(
        &temp_file_path,
        Some(&format!("status_{}", user_id)),
        None,
        None,
    ).await?;

    log::info!("Cloudinary upload successful: {}", upload_result.secure_url);

    // Clean up temp file
    if let Err(e) = std::fs::remove_file(&temp_file_path) {
        log::warn!("Failed to remove temp file: {}", e);
    }

    status_req.content_url = Some(upload_result.secure_url);
    status_req.content_type = if let Some(ref ct) = content_type {
        match ct.type_() {
            mime::IMAGE => StatusContentType::Image,
            mime::VIDEO => StatusContentType::Video,
            mime::AUDIO => StatusContentType::Audio,
            _ => StatusContentType::Video // Default fallback
        }
    } else {
        StatusContentType::Video // Default fallback
    };

    let status = data.status_service.create_status(&user_id, status_req).await?;

    let response = ApiResponse {
        success: true,
        message: "Status with media created successfully".to_string(),
        data: Some(status),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Get contact statuses (status feed)
pub async fn get_contact_statuses(
    data: web::Data<StatusControllerState>,
    query: web::Query<StatusQuery>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {

    let user_id = get_user_id_with_fallbacks(&req, query.user_id.as_deref(), None, None, &data.user_service).await?;

    // Get status summaries for contacts using contact service
    let summaries = if let Some(contact_ids_str) = &query.contact_ids {
        // Legacy mode: use provided contact IDs
        let contact_ids: Vec<String> = contact_ids_str.split(',').map(|s| s.trim().to_string()).collect();
        data.status_service.get_contact_statuses(&user_id, &contact_ids).await?
    } else {
        // New mode: use uploaded contacts from contact service
        data.status_service.get_contact_statuses_from_contacts(&user_id).await?
    };
    let count = summaries.len() as u32;

    let response = ApiResponse {
        success: true,
        message: "Contact statuses retrieved successfully".to_string(),
        data: None,
        entities: Some(summaries),
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: Some(count),
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Get statuses for a specific user
pub async fn get_user_statuses(
    data: web::Data<StatusControllerState>,
    path: web::Path<String>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    let target_user_id = path.into_inner();
    
    let viewer_id = get_user_id_with_fallbacks(&req, None, None, None, &data.user_service).await?;

    let statuses = data.status_service.get_user_statuses_enriched(&target_user_id, &viewer_id).await?;
    let count = statuses.len() as u32;

    let response = ApiResponse {
        success: true,
        message: "User statuses retrieved successfully".to_string(),
        data: None,
        entities: Some(statuses),
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: Some(count),
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Get my statuses
pub async fn get_my_statuses(
    data: web::Data<StatusControllerState>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {

    let user_id = get_user_id_with_fallbacks(&req, None, None, None, &data.user_service).await?;

    let statuses = data.status_service.get_my_statuses(&user_id).await?;
    let count = statuses.len() as u32;

    let response = ApiResponse {
        success: true,
        message: "My statuses retrieved successfully".to_string(),
        data: None,
        entities: Some(statuses),
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: Some(count),
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// View a status
pub async fn view_status(
    data: web::Data<StatusControllerState>,
    view_data: web::Json<ViewStatusRequest>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    let viewer_id = get_user_id_with_fallbacks(&req, None, None, None, &data.user_service).await?;

    data.status_service.view_status(&view_data.status_id, &viewer_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Status viewed successfully".to_string(),
        data: Some(serde_json::json!({
            "viewed_at": Utc::now().timestamp_millis()
        })),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Get status views (who viewed my status)
pub async fn get_status_views(
    data: web::Data<StatusControllerState>,
    path: web::Path<String>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    let status_id = path.into_inner();

    let user_id = get_user_id_with_fallbacks(&req, None, None, None, &data.user_service).await?;

    let views = data.status_service.get_status_views(&status_id, &user_id).await?;
    let count = views.len() as u32;

    let response = ApiResponse {
        success: true,
        message: "Status views retrieved successfully".to_string(),
        data: None,
        entities: Some(views),
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: Some(count),
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Delete a status
pub async fn delete_status(
    data: web::Data<StatusControllerState>,
    path: web::Path<String>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    let status_id = path.into_inner();

    let user_id = get_user_id_with_fallbacks(&req, None, None, None, &data.user_service).await?;

    data.status_service.delete_status(&status_id, &user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Status deleted successfully".to_string(),
        data: Some(serde_json::json!({
            "deleted_at": Utc::now().timestamp_millis()
        })),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Update privacy settings
pub async fn update_privacy_settings(
    data: web::Data<StatusControllerState>,
    privacy_data: web::Json<UpdateStatusPrivacyRequest>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {

    let user_id = get_user_id_with_fallbacks(&req, None, None, None, &data.user_service).await?;

    let settings = data.status_service.update_privacy_settings(&user_id, privacy_data.into_inner()).await?;

    let response = ApiResponse {
        success: true,
        message: "Privacy settings updated successfully".to_string(),
        data: Some(settings),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Get privacy settings
pub async fn get_privacy_settings(
    data: web::Data<StatusControllerState>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    let user_id = get_user_id_with_fallbacks(&req, None, None, None, &data.user_service).await?;

    let settings = data.status_service.get_user_privacy_settings(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Privacy settings retrieved successfully".to_string(),
        data: Some(settings),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

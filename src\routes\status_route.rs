use actix_web::web;
use crate::controllers::status_controller;

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/status")
            // Create status (text only)
            .route("", web::post().to(status_controller::create_status))
            
            // Upload status with media (image/video)
            .route("/upload", web::post().to(status_controller::upload_status_media))
            
            // Get contact statuses (status feed)
            .route("/feed", web::get().to(status_controller::get_contact_statuses))
            
            // Get my statuses
            .route("/my", web::get().to(status_controller::get_my_statuses))
            
            // Get statuses for a specific user
            .route("/user/{user_id}", web::get().to(status_controller::get_user_statuses))
            
            // View a status (mark as viewed)
            .route("/view", web::post().to(status_controller::view_status))
            
            // Get who viewed a specific status
            .route("/{status_id}/views", web::get().to(status_controller::get_status_views))
            
            // Delete a status
            .route("/{status_id}", web::delete().to(status_controller::delete_status))
            
            // Privacy settings routes
            .service(
                web::scope("/privacy")
                    .route("", web::get().to(status_controller::get_privacy_settings))
                    .route("", web::put().to(status_controller::update_privacy_settings))
            )
    );
}

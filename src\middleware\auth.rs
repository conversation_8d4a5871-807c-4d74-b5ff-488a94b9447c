use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    error::Error,
    http::header,
    HttpMessage,
    HttpResponse,
};
use chrono::{Duration, Utc};
use futures::future::{ok, Ready};
use jsonwebtoken::{decode, encode, Decoding<PERSON><PERSON>, <PERSON>co<PERSON><PERSON><PERSON>, <PERSON>er, Validation, Algorithm};
use log::{error, warn, info};
use serde::{Deserialize, Serialize};
use std::env;
use std::future::Future;
use std::pin::Pin;
use std::collections::HashSet;
use once_cell::sync::Lazy;

use crate::errors::AppError;

// Define a static set of public paths that don't require authentication
static PUBLIC_PATHS: Lazy<HashSet<&'static str>> = Lazy::new(|| {
    let mut set = HashSet::new();

    // Auth endpoints - public authentication routes
    set.insert("/api/v1/auth/register");
    set.insert("/api/v1/auth/login");
    set.insert("/api/v1/auth/verify");
    set.insert("/api/v1/auth/verify-login");
    set.insert("/api/v1/auth/refresh-token");
    set.insert("/api/v1/auth/resend-code");

    // Public user endpoints
    set.insert("/api/v1/users/chat-cred");

    // Agora direct login endpoint
    set.insert("/api/v1/chat/agora-login");

    // Documentation - always public
    set.insert("/api/v1/docs");
    set.insert("/api/v1/docs/*");
    set.insert("/api/v1/swagger.json");
    set.insert("/api/v1/swagger.yaml");
    set.insert("/swagger");
    set.insert("/swagger-ui");
    set.insert("/swagger-ui/*");
    set.insert("/api-docs");
    set.insert("/api-docs/*");

    // Static files
    set.insert("/profiles");
    set.insert("/profiles/*");

    set
});

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Claims {
    pub phone: String,      // Phone number (used as the primary identifier)
    pub exp: i64,           // Expiration time
    pub iat: i64,           // Issued at time
    pub jti: String,        // JWT ID (for token revocation)
    pub token_type: String, // Token type: "access" or "refresh"
    #[serde(skip_serializing_if = "Vec::is_empty", default)]
    pub roles: Vec<String>, // User roles for authorization (hidden for regular users)
}

pub struct Auth;

impl Auth {
    // Generate both access and refresh tokens
    pub fn generate_tokens(phone: &str, roles: Vec<String>) -> Result<(String, String), AppError> {
        // Generate access token (short-lived)
        let access_token = Self::generate_access_token(phone, roles.clone())?;

        // Generate refresh token (long-lived)
        let refresh_token = Self::generate_refresh_token(phone)?;

        Ok((access_token, refresh_token))
    }

    // Generate access token (short-lived)
    pub fn generate_access_token(phone: &str, roles: Vec<String>) -> Result<String, AppError> {
        let secret = env::var("JWT_SECRET")
            .map_err(|_| AppError::InternalError("JWT_SECRET environment variable is required".to_string()))?;

        // Generate a unique token ID
        let token_id = uuid::Uuid::new_v4().to_string();

        let now = Utc::now();
        // Access tokens are short-lived (1 hour by default)
        let access_token_expiry_minutes = env::var("ACCESS_TOKEN_EXPIRY_MINUTES")
            .unwrap_or_else(|_| "60".to_string()) // 1 hour default
            .parse::<i64>()
            .unwrap_or(60);

        let expires_at = now + Duration::minutes(access_token_expiry_minutes);

        // Only include roles if the user has special privileges
        // If the user only has the "user" role, don't include roles in the token
        let filtered_roles = if roles.len() == 1 && roles.contains(&"user".to_string()) {
            Vec::new() // Empty vector will be skipped in serialization
        } else {
            roles
        };

        let claims = Claims {
            phone: phone.to_string(),
            exp: expires_at.timestamp(),
            iat: now.timestamp(),
            jti: token_id,
            token_type: "access".to_string(),
            roles: filtered_roles,
        };

        // Use HS256 algorithm explicitly
        let header = Header::new(Algorithm::HS256);

        encode(
            &header,
            &claims,
            &EncodingKey::from_secret(secret.as_bytes()),
        )
        .map_err(|e| AppError::AuthError(format!("Failed to generate access token: {}", e)))
    }

    // Generate refresh token (long-lived)
    pub fn generate_refresh_token(phone: &str) -> Result<String, AppError> {
        // Always use JWT_SECRET for refresh tokens to ensure consistency
        let jwt_secret = env::var("JWT_SECRET")
            .map_err(|_| AppError::InternalError("JWT_SECRET environment variable is required".to_string()))?;

        // Generate a unique token ID
        let token_id = uuid::Uuid::new_v4().to_string();

        let now = Utc::now();
        // Refresh tokens are long-lived (7 days by default)
        let refresh_token_expiry_days = env::var("REFRESH_TOKEN_EXPIRY_DAYS")
            .unwrap_or_else(|_| "7".to_string()) // 7 days default
            .parse::<i64>()
            .unwrap_or(7);

        let expires_at = now + Duration::days(refresh_token_expiry_days);

        let claims = Claims {
            phone: phone.to_string(),
            exp: expires_at.timestamp(),
            iat: now.timestamp(),
            jti: token_id,
            token_type: "refresh".to_string(),
            roles: Vec::new(), // No roles in refresh token for security
        };

        // Use HS256 algorithm explicitly
        let header = Header::new(Algorithm::HS256);

        info!("Generating refresh token for phone: {}", phone);

        encode(
            &header,
            &claims,
            &EncodingKey::from_secret(jwt_secret.as_bytes()),
        )
        .map_err(|e| AppError::AuthError(format!("Failed to generate refresh token: {}", e)))
    }

    // Legacy method for backward compatibility
    pub fn generate_token(user_id: &str, phone: Option<String>, roles: Vec<String>) -> Result<String, AppError> {
        if let Some(phone_str) = phone {
            Self::generate_access_token(&phone_str, roles)
        } else {
            warn!("Using user_id as phone for token generation (legacy method)");
            Self::generate_access_token(user_id, roles)
        }
    }

    pub fn validate_token(token: &str) -> Result<Claims, AppError> {
        // First try to decode as an access token
        match Self::validate_access_token(token) {
            Ok(claims) => Ok(claims),
            Err(access_err) => {
                // If that fails, try to decode as a refresh token
                match Self::validate_refresh_token(token) {
                    Ok(claims) => Ok(claims),
                    Err(_) => {
                        // If both fail, return the access token error
                        Err(access_err)
                    }
                }
            }
        }
    }

    pub fn validate_access_token(token: &str) -> Result<Claims, AppError> {
        let secret = env::var("JWT_SECRET")
            .map_err(|_| AppError::InternalError("JWT_SECRET environment variable is required".to_string()))?;

        // Create a validation object with specific requirements
        let mut validation = Validation::new(Algorithm::HS256);
        validation.validate_exp = true;
        validation.validate_nbf = true;
        validation.leeway = 60; // 60 seconds of leeway for clock skew

        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(secret.as_bytes()),
            &validation,
        )
        .map_err(|e| AppError::AuthError(format!("Invalid access token: {}", e)))?;

        // Verify this is an access token
        if token_data.claims.token_type != "access" {
            return Err(AppError::AuthError("Token is not an access token".to_string()));
        }

        // Check if token has been revoked (would need to implement a token blacklist)
        // This would typically check a Redis cache or database for revoked tokens
        // For now, we'll just return the claims

        Ok(token_data.claims)
    }

    pub fn validate_refresh_token(token: &str) -> Result<Claims, AppError> {
        // First try with REFRESH_TOKEN_SECRET, fallback to JWT_SECRET
        let refresh_secret = env::var("REFRESH_TOKEN_SECRET").unwrap_or_else(|_| {
            env::var("JWT_SECRET")
                .expect("Either REFRESH_TOKEN_SECRET or JWT_SECRET environment variable is required")
        });

        // Create a validation object with specific requirements
        let mut validation = Validation::new(Algorithm::HS256);
        validation.validate_exp = true;
        validation.validate_nbf = false; // Don't validate nbf (not before) claim
        validation.leeway = 60; // 60 seconds of leeway for clock skew
        validation.required_spec_claims.remove("nbf"); // Remove nbf from required claims

        // First try with REFRESH_TOKEN_SECRET
        let token_result = decode::<Claims>(
            token,
            &DecodingKey::from_secret(refresh_secret.as_bytes()),
            &validation,
        );

        // If that fails, try with JWT_SECRET as fallback
        let token_data = match token_result {
            Ok(data) => data,
            Err(e) => {
                warn!("Failed to decode with REFRESH_TOKEN_SECRET: {}", e);

                // Try with JWT_SECRET as fallback
                let jwt_secret = env::var("JWT_SECRET").unwrap_or_else(|_| {
                    warn!("JWT_SECRET not set, using default secret. This is insecure for production!");
                    "default_secret".to_string()
                });

                decode::<Claims>(
                    token,
                    &DecodingKey::from_secret(jwt_secret.as_bytes()),
                    &validation,
                )
                .map_err(|e| AppError::AuthError(format!("Invalid refresh token: {}", e)))?
            }
        };

        // Verify this is a refresh token
        if token_data.claims.token_type != "refresh" {
            return Err(AppError::AuthError("Token is not a refresh token".to_string()));
        }

        // Log successful token validation
        info!("Successfully validated refresh token for phone: {}", token_data.claims.phone);

        // Check if token has been revoked (would need to implement a token blacklist)
        // This would typically check a Redis cache or database for revoked tokens
        // For now, we'll just return the claims

        Ok(token_data.claims)
    }

    // Check if a user has a specific role
    pub fn has_role(claims: &Claims, role: &str) -> bool {
        // If the requested role is "user", assume all authenticated users have this role
        if role == "user" {
            return true;
        }

        // Otherwise, check if the role exists in the roles list
        claims.roles.contains(&role.to_string())
    }

    // Check if a path is public (doesn't require authentication)
    pub fn is_public_path(path: &str) -> bool {
        // Exact match
        if PUBLIC_PATHS.contains(path) {
            return true;
        }

        // Check for path prefixes
        for public_path in PUBLIC_PATHS.iter() {
            if public_path.ends_with("*") && path.starts_with(&public_path[..public_path.len()-1]) {
                return true;
            }
        }

        false
    }
}

pub struct AuthMiddleware;

impl<S, B> Transform<S, ServiceRequest> for AuthMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = AuthMiddlewareService<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ok(AuthMiddlewareService { service })
    }
}

pub struct AuthMiddlewareService<S> {
    service: S,
}

impl<S, B> Service<ServiceRequest> for AuthMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>>>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let path = req.path().to_string();

        // Skip authentication for public endpoints
        if Auth::is_public_path(&path) {
            info!("Public path accessed: {}", path);
            let fut = self.service.call(req);
            return Box::pin(async move {
                let res = fut.await?;
                Ok(res)
            });
        }

        // CSRF token validation is temporarily disabled
        // This will be re-implemented in a more user-friendly way in the future
        if !req.method().is_safe() {
            // Log that we're skipping CSRF validation
            info!("CSRF validation disabled for request to {}", path);

            // Check if CSRF tokens are present (for logging purposes only)
            let csrf_header = req.headers().get("X-CSRF-Token");
            let csrf_cookie = req.cookie("csrf_token");

            if csrf_header.is_some() && csrf_cookie.is_some() {
                info!("CSRF tokens were provided but not validated");
            }
        }

        // Get the Authorization header
        let auth_header = req.headers().get(header::AUTHORIZATION);

        if let Some(auth_header) = auth_header {
            // Extract the token from the Authorization header
            let auth_str = match auth_header.to_str() {
                Ok(s) => s,
                Err(_) => {
                    warn!("Invalid Authorization header format in request to {}", path);
                    return Box::pin(async move {
                        Err(actix_web::error::InternalError::from_response(
                            "",
                            HttpResponse::Unauthorized()
                                .json(crate::middleware::error_handler::JsonErrorResponse {
                                    success: false,
                                    message: "Authentication error: Invalid Authorization header format".to_string(),
                                })
                        ).into())
                    });
                }
            };

            if auth_str.starts_with("Bearer ") {
                let token = &auth_str[7..];

                // Validate the token
                match Auth::validate_token(token) {
                    Ok(claims) => {
                        // Check token expiration
                        let now = Utc::now().timestamp();
                        if claims.exp < now {
                            warn!("Expired token used in request to {}", path);
                            return Box::pin(async move {
                                Err(actix_web::error::InternalError::from_response(
                                    "",
                                    HttpResponse::Unauthorized()
                                        .json(crate::middleware::error_handler::JsonErrorResponse {
                                            success: false,
                                            message: "Authentication error: Token has expired".to_string(),
                                        })
                                ).into())
                            });
                        }

                        // Ensure claims has at least the "user" role for authorization checks
                        let mut claims_with_default_role = claims.clone();
                        if claims_with_default_role.roles.is_empty() {
                            claims_with_default_role.roles = vec!["user".to_string()];
                        }

                        // Add the claims to the request extensions for use in handlers
                        req.extensions_mut().insert(claims_with_default_role.clone());

                        // Add phone number to the request extensions for easier access
                        req.extensions_mut().insert(claims.phone.clone());

                        // Check for required roles if path has role restrictions
                        // This would be implemented with a role-based access control system
                        // For now, we'll just proceed with the request

                        let fut = self.service.call(req);
                        return Box::pin(async move {
                            let res = fut.await?;
                            Ok(res)
                        });
                    }
                    Err(e) => {
                        error!("Token validation failed for request to {}: {}", path, e);
                        let error_message = format!("Authentication error: {}", e);
                        return Box::pin(async move {
                            Err(actix_web::error::InternalError::from_response(
                                "",
                                HttpResponse::Unauthorized()
                                    .json(crate::middleware::error_handler::JsonErrorResponse {
                                        success: false,
                                        message: error_message,
                                    })
                            ).into())
                        });
                    }
                }
            } else {
                warn!("Invalid Authorization header format (missing Bearer) in request to {}", path);
                return Box::pin(async move {
                    Err(actix_web::error::InternalError::from_response(
                        "",
                        HttpResponse::Unauthorized()
                            .json(crate::middleware::error_handler::JsonErrorResponse {
                                success: false,
                                message: "Authentication error: Invalid Authorization header format (missing Bearer)".to_string(),
                            })
                    ).into())
                });
            }
        } else {
            warn!("No Authorization header in request to {}", path);
        }

        // No valid token found
        Box::pin(async move {
            Err(actix_web::error::InternalError::from_response(
                "",
                HttpResponse::Unauthorized()
                    .json(crate::middleware::error_handler::JsonErrorResponse {
                        success: false,
                        message: "Authentication error: Authentication required".to_string(),
                    })
            ).into())
        })
    }
}

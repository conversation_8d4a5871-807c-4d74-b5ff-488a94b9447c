use actix_web::{web, HttpResponse, HttpRequest, HttpMessage};
use actix_multipart::Multipart;
use chrono::Utc;
use futures::StreamExt;

use std::sync::Arc;
use tokio::sync::Mutex;

// use jsonwebtoken::encode;
use serde::{Deserialize, Serialize};


use crate::errors::AppError;
use crate::models::{
    ApiResponse, RegisterRequest, VerifyRequest, UpdateProfileRequest, LoginRequest, VerifyLoginRequest,
    User, NotificationSettings, PrivacySettings, AppearanceSettings
};
use crate::services::{
    UserService, TwilioService, CloudinaryUploader, UserSettingsService, KeysService
};
use serde_json::json;
use std::collections::HashMap;

// JWT Claims
#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String, 
    phone: String, 
    exp: usize, 
    iat: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserQuery {
    pub phone: Option<String>,
    pub user_id: Option<String>,  
}

pub struct UsersControllerState {
    pub user_service: Arc<UserService>,
    pub twilio_service: Arc<TwilioService>,
    pub cloudinary_uploader: Arc<CloudinaryUploader>,
    pub user_settings_service: Arc<UserSettingsService>,
    pub keys_service: Arc<KeysService>,
}

// Register a new user
pub async fn register(
    data: web::Data<UsersControllerState>,
    register_data: web::Json<RegisterRequest>,
) -> Result<HttpResponse, AppError> {

    if register_data.phone.is_empty() {
        return Err(AppError::ValidationError("Phone number is required".to_string()));
    }

    let trimmed_phone = register_data.phone.trim();
    let formatted_phone = if !trimmed_phone.starts_with('+') {
        format!("+{}", trimmed_phone)
    } else {
        trimmed_phone.to_string()
    };

    log::info!("Registering user with formatted phone: '{}'", formatted_phone);

    // Check if user already exists - only allow new phone numbers for registration
    match data.user_service.get_user_by_phone(&formatted_phone).await {
        Ok(_existing_user) => {
            log::warn!("Registration failed: Phone number {} already exists", formatted_phone);
            return Ok(HttpResponse::BadRequest().json(ApiResponse {
                success: false,
                message: "Already Registered.".to_string(),
                data: Some(serde_json::json!({
                    "phone": formatted_phone,
                    "error_code": "PHONE_ALREADY_EXISTS",
                    "suggestion": "Use the login endpoint instead"
                })),
                entities: None,
                timestamp: Some(Utc::now().timestamp() as u64),
                duration: None,
                count: None,
                cursor: None,
            }));
        },
        Err(AppError::NotFoundError(_)) => {
            // Phone number doesn't exist, proceed with registration
            log::info!("Phone number {} is available for registration", formatted_phone);
        },
        Err(e) => {
            // Other database errors
            log::error!("Database error while checking phone number {}: {}", formatted_phone, e);
            return Err(e);
        }
    }

    let code = data.user_service.create_verification(&formatted_phone).await?;

    // Try to send SMS, but don't fail if it doesn't work (for development)
    let sms_sent = match data.twilio_service.send_otp(&formatted_phone, &code).await {
        Ok(_) => {
            log::info!("SMS sent successfully to {}", formatted_phone);
            true
        },
        Err(e) => {
            log::warn!("Failed to send SMS to {}: {}. Code will be included in response for development.", formatted_phone, e);
            false
        }
    };

    // Check if we're in development mode
    let is_development = std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string()) == "development"
        || std::env::var("INCLUDE_CODE_IN_RESPONSE").unwrap_or_else(|_| "false".to_string()) == "true"
        || !sms_sent; // Always include code if SMS failed

    let mut response_data = serde_json::json!({
        "phone": formatted_phone,
        "sms_sent": sms_sent
    });

    // Include verification code in development or if SMS failed
    if is_development {
        response_data["verification_code"] = serde_json::json!(code);
        response_data["note"] = serde_json::json!("Will remove the code in response soon");
    }

    let response = ApiResponse {
        success: true,
        message: if sms_sent {
            "Verification code sent".to_string()
        } else {
            "Verification code generated (SMS failed - code included in response)".to_string()
        },
        data: Some(response_data),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Verify a code and create/update user
pub async fn verify(
    data: web::Data<UsersControllerState>,
    verify_data: web::Json<VerifyRequest>,
) -> Result<HttpResponse, AppError> {

    if verify_data.phone.is_empty() {
        return Err(AppError::ValidationError("Phone number is required".to_string()));
    }

    if verify_data.code.is_empty() {
        return Err(AppError::ValidationError("Verification code is required".to_string()));
    }

    let trimmed_phone = verify_data.phone.trim();
    let formatted_phone = if !trimmed_phone.starts_with('+') {
        format!("+{}", trimmed_phone)
    } else {
        trimmed_phone.to_string()
    };

    log::info!("Verifying code for user with formatted phone: '{}'", formatted_phone);

    let verified = data.user_service.verify_code(&formatted_phone, &verify_data.code).await?;

    if !verified {
        return Err(AppError::ValidationError("Invalid verification code".to_string()));
    }

    let user = data.user_service.create_user(&formatted_phone).await?;

    let auth_tokens = generate_jwt_tokens(&user)?;

    let safe_user = data.user_service.to_safe_user_response(user);

    let response = ApiResponse {
        success: true,
        message: "User verified and logged in".to_string(),
        data: Some(serde_json::json!({
            "user": safe_user,
            "auth_tokens": auth_tokens
        })),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Login a user
pub async fn login(
    data: web::Data<UsersControllerState>,
    login_data: web::Json<LoginRequest>,
) -> Result<HttpResponse, AppError> {

    if login_data.phone.is_empty() {
        return Err(AppError::ValidationError("Phone number is required".to_string()));
    }

    let trimmed_phone = login_data.phone.trim();
    let formatted_phone = if !trimmed_phone.starts_with('+') {
        format!("+{}", trimmed_phone)
    } else {
        trimmed_phone.to_string()
    };

    log::info!("=== LOGIN DEBUG ===");
    log::info!("Original phone from request: '{}'", login_data.phone);
    log::info!("Trimmed phone: '{}'", trimmed_phone);
    log::info!("Formatted phone: '{}'", formatted_phone);
    log::info!("Phone bytes: {:?}", formatted_phone.as_bytes());

    let user = match data.user_service.get_user_by_phone(&formatted_phone).await {
        Ok(user) => user,
        Err(AppError::NotFoundError(_)) => {
            log::warn!("Login failed: Phone number {} not found", formatted_phone);
            return Ok(HttpResponse::BadRequest().json(ApiResponse {
                success: false,
                message: "Phone number not registered. Please register first.".to_string(),
                data: Some(serde_json::json!({
                    "phone": formatted_phone,
                    "error_code": "PHONE_NOT_FOUND",
                    "suggestion": "Use the register endpoint first"
                })),
                entities: None,
                timestamp: Some(Utc::now().timestamp() as u64),
                duration: None,
                count: None,
                cursor: None,
            }));
        },
        Err(e) => {
            log::error!("Database error while checking phone number {}: {}", formatted_phone, e);
            return Err(e);
        }
    };

    if !user.is_verified {
        return Err(AppError::AuthenticationError("User not verified".to_string()));
    }

    let code = data.user_service.create_verification(&formatted_phone).await?;

    // Try to send SMS, but don't fail if it doesn't work (for development)
    let sms_sent = match data.twilio_service.send_otp(&formatted_phone, &code).await {
        Ok(_) => {
            log::info!("SMS sent successfully to {}", formatted_phone);
            true
        },
        Err(e) => {
            log::warn!("Failed to send SMS to {}: {}. Code will be included in response for development.", formatted_phone, e);
            false
        }
    };

    // Check if we're in development mode (you can also use NODE_ENV or RUST_ENV)
    let is_development = std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string()) == "development"
        || std::env::var("INCLUDE_CODE_IN_RESPONSE").unwrap_or_else(|_| "false".to_string()) == "true"
        || !sms_sent; // Always include code if SMS failed

    let mut response_data = serde_json::json!({
        "phone": formatted_phone,
        "sms_sent": sms_sent
    });

    // Include verification code in development or if SMS failed
    if is_development {
        response_data["verification_code"] = serde_json::json!(code);
        response_data["note"] = serde_json::json!("Will remove the code in response soon");
    }

    let response = ApiResponse {
        success: true,
        message: if sms_sent {
            "Verification code sent".to_string()
        } else {
            "Verification code generated (SMS failed - code included in response)".to_string()
        },
        data: Some(response_data),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Resend verification code
pub async fn resend_verification_code(
    data: web::Data<UsersControllerState>,
    resend_data: web::Json<RegisterRequest>,
) -> Result<HttpResponse, AppError> {

    if resend_data.phone.is_empty() {
        return Err(AppError::ValidationError("Phone number is required".to_string()));
    }

    let trimmed_phone = resend_data.phone.trim();
    let formatted_phone = if !trimmed_phone.starts_with('+') {
        format!("+{}", trimmed_phone)
    } else {
        trimmed_phone.to_string()
    };

    log::info!("Resending verification code for phone: '{}'", formatted_phone);

    let code = data.user_service.create_verification(&formatted_phone).await?;

    // Try to send SMS, but don't fail if it doesn't work (for development)
    let sms_sent = match data.twilio_service.send_otp(&formatted_phone, &code).await {
        Ok(_) => {
            log::info!("SMS sent successfully to {}", formatted_phone);
            true
        },
        Err(e) => {
            log::warn!("Failed to send SMS to {}: {}. Code will be included in response for development.", formatted_phone, e);
            false
        }
    };

    // Check if we're in development mode
    let is_development = std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string()) == "development"
        || std::env::var("INCLUDE_CODE_IN_RESPONSE").unwrap_or_else(|_| "false".to_string()) == "true"
        || !sms_sent; // Always include code if SMS failed

    let mut response_data = serde_json::json!({
        "phone": formatted_phone,
        "sms_sent": sms_sent
    });

    // Include verification code in development or if SMS failed
    if is_development {
        response_data["verification_code"] = serde_json::json!(code);
        response_data["note"] = serde_json::json!("Will remove the code in response soon");
    }

    let response = ApiResponse {
        success: true,
        message: if sms_sent {
            "Verification code resent".to_string()
        } else {
            "Verification code generated (SMS failed - code included in response)".to_string()
        },
        data: Some(response_data),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Verify login code and get user data
pub async fn verify_login(
    data: web::Data<UsersControllerState>,
    verify_data: web::Json<VerifyLoginRequest>,
) -> Result<HttpResponse, AppError> {

    if verify_data.phone.is_empty() {
        return Err(AppError::ValidationError("Phone number is required".to_string()));
    }

    if verify_data.code.is_empty() {
        return Err(AppError::ValidationError("Verification code is required".to_string()));
    }

    let trimmed_phone = verify_data.phone.trim();
    let formatted_phone = if !trimmed_phone.starts_with('+') {
        format!("+{}", trimmed_phone)
    } else {
        trimmed_phone.to_string()
    };

    log::info!("Verifying login for user with formatted phone: '{}'", formatted_phone);

    let verified = data.user_service.verify_code(&formatted_phone, &verify_data.code).await?;

    if !verified {
        return Err(AppError::ValidationError("Invalid verification code".to_string()));
    }

    let user = data.user_service.get_user_by_phone(&formatted_phone).await?;

    let auth_tokens = generate_jwt_tokens(&user)?;


    let safe_user = data.user_service.to_safe_user_response(user);

    let response = ApiResponse {
        success: true,
        message: "Login verified successfully".to_string(),
        data: Some(serde_json::json!({
            "user": safe_user,
            "auth_tokens": auth_tokens
        })),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Get current user
pub async fn get_current_user(
    data: web::Data<UsersControllerState>,
    query: web::Query<UserQuery>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    // Check if phone is provided in query parameters
    if let Some(phone) = &query.phone {
        // Clean up the phone number by trimming spaces and ensuring it has a + prefix
        let trimmed_phone = phone.trim();
        let formatted_phone = if !trimmed_phone.starts_with('+') {
            format!("+{}", trimmed_phone)
        } else {
            trimmed_phone.to_string()
        };

        // Log the formatted phone number for debugging
        log::info!("Looking up user with formatted phone: '{}'", formatted_phone);

        // Get user by phone number
        let user = data.user_service.get_user_by_phone(&formatted_phone).await?;

        // Update last seen
        if let Some(id) = &user.id {
            let user_id = id.to_hex();
            data.user_service.update_last_seen(&user_id).await?;
        }

        // Convert to safe user response
        let safe_user = data.user_service.to_safe_user_response(user);

        let response = ApiResponse {
            success: true,
            message: "User retrieved".to_string(),
            data: Some(safe_user),
            entities: None,
            timestamp: Some(Utc::now().timestamp() as u64),
            duration: None,
            count: None,
            cursor: None,
        };

        return Ok(HttpResponse::Ok().json(response));
    }

    // Try to get user ID with fallbacks (from query, JWT token, etc.)
    let user_id = get_user_id_with_fallbacks(
        &req,
        None,
        Some(&query as &dyn std::any::Any),
        None,
        &data.user_service,
    ).await?;

    // Get user
    let user = data.user_service.get_user_by_id(&user_id).await?;

    // Update last seen
    data.user_service.update_last_seen(&user_id).await?;

    // Convert to safe user response
    let safe_user = data.user_service.to_safe_user_response(user);

    let response = ApiResponse {
        success: true,
        message: "User retrieved".to_string(),
        data: Some(safe_user),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Update user profile
pub async fn update_profile(
    data: web::Data<UsersControllerState>,
    profile_data: web::Json<UpdateProfileRequest>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    // Get user ID with fallbacks
    let user_id = get_user_id_with_fallbacks(
        &req,
        profile_data.user_id.as_deref(),
        None,
        None,
        &data.user_service,
    ).await?;

    // Update user profile
    let user = data.user_service.update_user_profile(
        &user_id,
        profile_data.username.clone(),
        profile_data.profile_picture.clone(),
        profile_data.status.clone(),
        profile_data.bio.clone(),
        profile_data.address.clone(),
    ).await?;



    // Convert to safe user response
    let safe_user = data.user_service.to_safe_user_response(user);

    let response = ApiResponse {
        success: true,
        message: "Profile updated".to_string(),
        data: Some(safe_user),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Upload profile picture
pub async fn upload_profile_picture(
    data: web::Data<UsersControllerState>,
    mut payload: Multipart,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    log::info!("=== PROFILE PICTURE UPLOAD STARTED ===");

    // Log request headers for debugging
    log::info!("Request headers:");
    for (header_name, header_value) in req.headers() {
        log::info!("  {}: {}", header_name, header_value.to_str().unwrap_or("Invalid header value"));
    }

    // Initialize the temp storage service
    let temp_storage = crate::services::TempStorageService::new();
    log::info!("Temp storage service initialized");

    // First, try to extract user_id from the form data
    let mut user_id_from_form: Option<String> = None;

    // Store the profile picture field path
    let mut profile_picture_path: Option<(std::path::PathBuf, Option<mime::Mime>)> = None;

    // Process the multipart form
    let mut has_items = false;
    log::info!("Processing multipart form data...");

    // Ensure the temp directory exists before processing
    let temp_dir = "temp/uploads";
    if !std::path::Path::new(temp_dir).exists() {
        log::info!("Creating temp directory: {}", temp_dir);
        std::fs::create_dir_all(temp_dir).map_err(|e| {
            log::error!("Failed to create temp directory: {}", e);
            AppError::InternalError(format!("Failed to create temp directory: {}", e))
        })?;
    }

    while let Some(item) = payload.next().await {
        has_items = true;
        let field = match item {
            Ok(f) => f,
            Err(e) => {
                log::error!("Error processing form item: {}", e);
                if e.to_string().contains("Multipart boundary is not found") {
                    return Err(AppError::ValidationError("Please provide a profile picture file in the request".to_string()));
                } else if e.to_string().contains("payload") {
                    return Err(AppError::ValidationError("File size exceeds the maximum allowed size".to_string()));
                } else {
                    return Err(AppError::ValidationError(format!("Failed to process form: {}", e)));
                }
            }
        };

        let field_name = field.name().to_string();
        log::info!("Processing form field: {}", field_name);

        // Check if this field is the user_id
        if field_name == "user_id" {
            log::info!("Found user_id field");
            // Store the field in temp storage
            let (temp_path, _) = temp_storage.store_file(field).await.map_err(|e| {
                log::error!("Failed to store user_id field: {}", e);
                e
            })?;

            // Read the file content
            let buffer = temp_storage.read_file(&temp_path).map_err(|e| {
                log::error!("Failed to read user_id content: {}", e);
                e
            })?;

            if !buffer.is_empty() {
                // Convert bytes to string
                let user_id_str = String::from_utf8(buffer)
                    .map_err(|e| {
                        log::error!("Invalid user_id format: {}", e);
                        AppError::ValidationError(format!("Invalid user ID format: {}", e))
                    })?;

                if !user_id_str.is_empty() {
                    user_id_from_form = Some(user_id_str);
                    log::info!("Extracted user_id from form: {:?}", user_id_from_form);
                }
            }

            // Clean up the temp file
            if let Err(e) = temp_storage.delete_file(&temp_path) {
                log::warn!("Failed to delete temp file for user_id: {}", e);
            }
        } else if field_name == "profile_picture" {
            log::info!("Found profile_picture field");

            // Log content type and filename if available
            if let Some(content_type) = field.content_type() {
                log::info!("Content-Type: {}", content_type);
            }

            if let Some(filename) = field.content_disposition().get_filename() {
                log::info!("Filename: {}", filename);
            }

            // Check file size before storing
            let mut field_data = Vec::new();
            let mut field = field;

            // Read the field data in chunks
            log::info!("Reading profile picture data in chunks...");
            while let Some(chunk) = field.next().await {
                let data = chunk.map_err(|e| {
                    log::error!("Error reading file chunk: {}", e);
                    AppError::ValidationError(format!("Error reading file chunk: {}", e))
                })?;

                // Check if adding this chunk would exceed 10MB
                if field_data.len() + data.len() > 10 * 1024 * 1024 {
                    log::error!("Profile picture exceeds the maximum size of 10MB");
                    return Err(AppError::ValidationError(
                        "Profile picture exceeds the maximum size of 10MB".to_string()
                    ));
                }

                field_data.extend_from_slice(&data);
            }

            log::info!("Received profile picture: {} bytes", field_data.len());

            // Store the file in temp storage
            log::info!("Storing file in temp storage...");
            let result = temp_storage.store_bytes(&field_data, field.name(), field.content_type()).await
                .map_err(|e| {
                    log::error!("Failed to store file in temp storage: {}", e);
                    e
                })?;

            log::info!("File stored at: {:?}", result.0);
            profile_picture_path = Some(result);
        } else {
            log::warn!("Unexpected form field: {}", field_name);
        }
    }

    // Check if we received any form data
    if !has_items {
        log::error!("No form data received");
        return Err(AppError::ValidationError("Please provide a profile picture file in the request".to_string()));
    }

    // Get user ID with fallbacks, including the one from form data
    log::info!("Getting user ID with fallbacks...");
    let user_id = match user_id_from_form {
        // If user_id was provided directly in the form, use it
        Some(id) if !id.is_empty() => {
            log::info!("Using user_id from form data: {}", id);
            id
        },
        // Otherwise try the fallback mechanisms
        _ => {
            log::info!("No user_id in form data, trying fallbacks");
            get_user_id_with_fallbacks(
                &req,
                user_id_from_form.as_deref(),
                None,
                None,
                &data.user_service,
            ).await.map_err(|e| {
                log::error!("Failed to get user ID with fallbacks: {}", e);
                e
            })?
        }
    };

    log::info!("Using user_id: {}", user_id);

    // Check if we found a profile picture
    if profile_picture_path.is_none() {
        log::error!("No profile picture found in the request");
        return Err(AppError::ValidationError("No profile picture found in the request. Please include a file with the field name 'profile_picture'.".to_string()));
    }

    // Process the profile picture
    if let Some((file_path, content_type)) = profile_picture_path {
        log::info!("Processing profile picture at path: {:?}", file_path);
        log::info!("Content type: {:?}", content_type);

        // Read the file content
        log::info!("Reading file content...");
        let buffer = temp_storage.read_file(&file_path).map_err(|e| {
            log::error!("Failed to read file content: {}", e);
            e
        })?;

        log::info!("File content read: {} bytes", buffer.len());

        // Use the CloudinaryUploader directly
        log::info!("Using CloudinaryUploader for profile picture upload");

        // Check Cloudinary credentials
        log::info!("Checking Cloudinary credentials...");
        match data.cloudinary_uploader.check_credentials().await {
            Ok(true) => log::info!("Cloudinary credentials are valid"),
            Ok(false) => log::error!("Cloudinary credentials are invalid or incomplete"),
            Err(e) => log::error!("Failed to check Cloudinary credentials: {}", e),
        }

        // Force upload to Cloudinary, don't allow local fallback
        log::info!("Uploading to Cloudinary...");
        let result = match data.cloudinary_uploader.upload_profile_picture(&buffer, &user_id).await {
            Ok(url) => {
                if !url.contains("cloudinary.com") {
                    log::error!("Upload succeeded but URL doesn't contain cloudinary.com: {}", url);
                    Err(AppError::ExternalServiceError("Failed to upload to Cloudinary - invalid URL returned".to_string()))
                } else {
                    log::info!("Successfully uploaded profile picture to Cloudinary: {}", url);

                    // Update the user's profile picture in the database
                    let profile_picture = url.clone();
                    log::info!("Updating user profile with new picture URL...");

                    // Update profile picture in database
                    data.user_service.update_user_profile(&user_id, None, Some(profile_picture.clone()), None, None, None).await
                        .map_err(|e| {
                            log::error!("Failed to update user profile: {}", e);
                            e
                        })?;

                    // Return the response
                    log::info!("Getting updated user data...");
                    let user = data.user_service.get_user_by_id(&user_id).await
                        .map_err(|e| {
                            log::error!("Failed to get updated user data: {}", e);
                            e
                        })?;

                    log::info!("Profile picture upload completed successfully");
                    Ok(HttpResponse::Ok().json(json!({
                        "success": true,
                        "message": "Profile picture uploaded",
                        "data": {
                            "user": user,
                            "profile_picture_url": profile_picture
                        }
                    })))
                }
            },
            Err(e) => {
                log::error!("Failed to upload with CloudinaryUploader: {}", e);
                // Don't fall back to local storage, return the error
                Err(AppError::ExternalServiceError(format!("Failed to upload to Cloudinary: {}", e)))
            }
        };

        // Clean up the temp file
        log::info!("Cleaning up temp file...");
        if let Err(e) = temp_storage.delete_file(&file_path) {
            log::warn!("Failed to delete temp file: {}", e);
        } else {
            log::info!("Successfully deleted temp file");
        }

        // Return the result
        log::info!("=== PROFILE PICTURE UPLOAD COMPLETED ===");
        return result;
    }

    // This should never be reached due to the check above, but just in case
    log::error!("No profile picture found in the request (unexpected code path)");
    Err(AppError::ValidationError("No profile picture found in the request. Please include a file with the field name 'profile_picture'.".to_string()))
}

// Get user settings
pub async fn get_settings(
    data: web::Data<UsersControllerState>,
    req: HttpRequest,
    query: web::Query<UserQuery>,
) -> Result<HttpResponse, AppError> {
    // Get user ID with fallbacks
    let user_id = get_user_id_with_fallbacks(
        &req,
        None,
        Some(&query as &dyn std::any::Any),
        None,
        &data.user_service,
    ).await?;

    // Get user settings
    let settings = data.user_settings_service.get_user_settings(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Settings retrieved".to_string(),
        data: Some(settings),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Update notification settings
pub async fn update_notification_settings(
    data: web::Data<UsersControllerState>,
    settings: web::Json<NotificationSettings>,
    req: HttpRequest,
    query: web::Query<UserQuery>,
) -> Result<HttpResponse, AppError> {
    // Get user ID with fallbacks
    let user_id = get_user_id_with_fallbacks(
        &req,
        None,
        Some(&query as &dyn std::any::Any),
        None,
        &data.user_service,
    ).await?;

    // Update notification settings
    let updated_settings = data.user_settings_service.update_notification_settings(&user_id, settings.into_inner()).await?;

    let response = ApiResponse {
        success: true,
        message: "Notification settings updated".to_string(),
        data: Some(updated_settings),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Update privacy settings
pub async fn update_privacy_settings(
    data: web::Data<UsersControllerState>,
    settings: web::Json<PrivacySettings>,
    req: HttpRequest,
    query: web::Query<UserQuery>,
) -> Result<HttpResponse, AppError> {
    // Get user ID with fallbacks
    let user_id = get_user_id_with_fallbacks(
        &req,
        None,
        Some(&query as &dyn std::any::Any),
        None,
        &data.user_service,
    ).await?;

    // Update privacy settings
    let updated_settings = data.user_settings_service.update_privacy_settings(&user_id, settings.into_inner()).await?;

    let response = ApiResponse {
        success: true,
        message: "Privacy settings updated".to_string(),
        data: Some(updated_settings),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Update appearance settings
pub async fn update_appearance_settings(
    data: web::Data<UsersControllerState>,
    settings: web::Json<AppearanceSettings>,
    req: HttpRequest,
    query: web::Query<UserQuery>,
) -> Result<HttpResponse, AppError> {
    // Get user ID with fallbacks
    let user_id = get_user_id_with_fallbacks(
        &req,
        None,
        Some(&query as &dyn std::any::Any),
        None,
        &data.user_service,
    ).await?;

    // Update appearance settings
    let updated_settings = data.user_settings_service.update_appearance_settings(&user_id, settings.into_inner()).await?;

    let response = ApiResponse {
        success: true,
        message: "Appearance settings updated".to_string(),
        data: Some(updated_settings),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Update language
pub async fn update_language(
    data: web::Data<UsersControllerState>,
    language: web::Json<serde_json::Value>,
    req: HttpRequest,
    query: web::Query<UserQuery>,
) -> Result<HttpResponse, AppError> {
    // Get user ID with fallbacks
    let user_id = get_user_id_with_fallbacks(
        &req,
        None,
        Some(&query as &dyn std::any::Any),
        None,
        &data.user_service,
    ).await?;

    // Extract language from JSON
    let language_str = language.get("language")
        .and_then(|v| v.as_str())
        .ok_or_else(|| AppError::ValidationError("Language is required".to_string()))?;

    // Update language
    let updated_settings = data.user_settings_service.update_language(&user_id, language_str).await?;

    let response = ApiResponse {
        success: true,
        message: "Language updated".to_string(),
        data: Some(updated_settings),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Delete user account
pub async fn delete_user_account(
    req: HttpRequest,
    data: web::Data<UsersControllerState>,
) -> Result<HttpResponse, AppError> {
    // Get user ID from JWT token
    let user_id = get_user_id_with_fallbacks(&req, None, None, None, &data.user_service).await?;

    // Get the user
    let user = data.user_service.get_user_by_id(&user_id).await?;

    // Revoke encryption keys
    match data.keys_service.revoke_keys(&user_id).await {
        Ok(_) => log::info!("Revoked encryption keys for user: {}", user_id),
        Err(e) => log::error!("Failed to revoke encryption keys: {}", e),
    }

    // Delete verification records
    match data.user_service.delete_verification_records(&user.phone).await {
        Ok(_) => log::info!("Deleted verification records for phone: {}", user.phone),
        Err(e) => log::error!("Failed to delete verification records: {}", e),
    }

    // Delete user settings
    match data.user_settings_service.delete_user_settings(&user_id).await {
        Ok(_) => log::info!("Deleted settings for user: {}", user_id),
        Err(e) => log::error!("Failed to delete user settings: {}", e),
    }

    // Delete user from database
    data.user_service.delete_user(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "User account deleted successfully".to_string(),
        data: None::<String>,
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Generate encryption keys
pub async fn generate_keys(
    data: web::Data<UsersControllerState>,
    _req: HttpRequest,
    query: web::Query<UserQuery>,
) -> Result<HttpResponse, AppError> {
    // Get user ID directly from query parameters
    let user_id = if let Some(id) = &query.user_id {
        if !id.is_empty() {
            id.clone()
        } else {
            return Err(AppError::ValidationError("user_id is required".to_string()));
        }
    } else {
        return Err(AppError::ValidationError("user_id is required".to_string()));
    };

    // Generate keys
    let keys = data.keys_service.generate_keys(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Encryption keys generated".to_string(),
        data: Some(keys),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    // Set cache control headers
    Ok(HttpResponse::Ok()
        .append_header(("Cache-Control", "no-store"))
        .append_header(("Pragma", "no-cache"))
        .json(response))
}

// Get pre-key bundle
pub async fn get_pre_key_bundle(
    data: web::Data<UsersControllerState>,
    path: web::Path<String>,
) -> Result<HttpResponse, AppError> {
    let user_id = path.into_inner();

    // Retrieve public keys
    let bundle = data.keys_service.retrieve_public_keys(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Pre-key bundle retrieved".to_string(),
        data: Some(bundle),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Rotate keys
pub async fn rotate_keys(
    data: web::Data<UsersControllerState>,
    _req: HttpRequest,
    query: web::Query<UserQuery>,
) -> Result<HttpResponse, AppError> {
    // Get user ID directly from query parameters
    let user_id = if let Some(id) = &query.user_id {
        if !id.is_empty() {
            id.clone()
        } else {
            return Err(AppError::ValidationError("user_id is required".to_string()));
        }
    } else {
        return Err(AppError::ValidationError("user_id is required".to_string()));
    };

    // Rotate keys
    let keys = data.keys_service.rotate_keys(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Encryption keys rotated".to_string(),
        data: Some(keys),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    // Set cache control headers
    Ok(HttpResponse::Ok()
        .append_header(("Cache-Control", "no-store"))
        .append_header(("Pragma", "no-cache"))
        .json(response))
}

// Get session status
pub async fn get_session_status(
    data: web::Data<UsersControllerState>,
    path: web::Path<String>,
    query: web::Query<UserQuery>,
) -> Result<HttpResponse, AppError> {
    // Get user ID directly from query parameters
    let user_id = if let Some(id) = &query.user_id {
        if !id.is_empty() {
            id.clone()
        } else {
            return Err(AppError::ValidationError("user_id is required".to_string()));
        }
    } else {
        return Err(AppError::ValidationError("user_id is required".to_string()));
    };

    // Get contact ID from path
    let contact_id = path.into_inner();

    // Get session status
    let session = data.keys_service.get_session_status(&user_id, &contact_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Session status retrieved".to_string(),
        data: Some(serde_json::json!({
            "is_active": session.is_active,
            "last_updated": session.last_updated.timestamp(),
            "send_chain_length": session.send_chain_length,
            "receive_chain_length": session.receive_chain_length
        })),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Revoke keys
pub async fn revoke_keys(
    data: web::Data<UsersControllerState>,
    query: web::Query<UserQuery>,
) -> Result<HttpResponse, AppError> {
    // Get user ID directly from query parameters
    let user_id = if let Some(id) = &query.user_id {
        if !id.is_empty() {
            id.clone()
        } else {
            return Err(AppError::ValidationError("user_id is required".to_string()));
        }
    } else {
        return Err(AppError::ValidationError("user_id is required".to_string()));
    };

    // Revoke keys
    data.keys_service.revoke_keys(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Encryption keys revoked".to_string(),
        data: Some(serde_json::json!({
            "revoked_at": Utc::now().timestamp()
        })),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Helper function to generate JWT tokens (access and refresh)
fn generate_jwt_tokens(user: &User) -> Result<serde_json::Value, AppError> {
    // Ensure user has a phone number
    if user.phone.is_empty() {
        return Err(AppError::InternalError("User phone number is missing".to_string()));
    }

    // Get default roles for the user
    let roles = vec!["user".to_string()];

    // Generate both access and refresh tokens
    let (access_token, refresh_token) = crate::middleware::Auth::generate_tokens(&user.phone, roles)?;

    // Return both tokens as JSON
    Ok(serde_json::json!({
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "Bearer"
    }))
}

// Legacy function for backward compatibility
fn generate_jwt_token(user: &User) -> Result<String, AppError> {
    // Get default roles for the user
    let roles = vec!["user".to_string()];

    // Use the Auth middleware's token generation function
    crate::middleware::Auth::generate_access_token(&user.phone, roles)
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RefreshTokenRequest {
    pub refresh_token: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProfilePictureRequest {
    pub user_id: Option<String>,
}



// Refresh JWT tokens using a refresh token
pub async fn refresh_token(
    data: web::Data<UsersControllerState>,
    refresh_req: web::Json<RefreshTokenRequest>,
) -> Result<HttpResponse, AppError> {
    log::info!("Refresh token request received");

    // Validate the refresh token
    let claims = match crate::middleware::Auth::validate_refresh_token(&refresh_req.refresh_token) {
        Ok(claims) => {
            log::info!("Refresh token validated successfully for phone: {}", claims.phone);
            claims
        },
        Err(e) => {
            log::error!("Failed to validate refresh token: {}", e);
            return Err(AppError::AuthenticationError(format!("Invalid refresh token: {}", e)));
        }
    };

    // Get the phone number from the token
    let phone = claims.phone.clone();
    log::info!("Getting user data for phone: {}", phone);

    // Get user by phone number
    let user = match data.user_service.get_user_by_phone(&phone).await {
        Ok(user) => {
            log::info!("User found for phone: {}", phone);
            user
        },
        Err(e) => {
            log::error!("Failed to get user by phone {}: {}", phone, e);
            return Err(e);
        }
    };

    // Generate new JWT tokens (access and refresh)
    let auth_tokens = match generate_jwt_tokens(&user) {
        Ok(tokens) => {
            log::info!("New tokens generated successfully for user with phone: {}", phone);
            tokens
        },
        Err(e) => {
            log::error!("Failed to generate new tokens: {}", e);
            return Err(e);
        }
    };

    let response = ApiResponse {
        success: true,
        message: "Tokens refreshed successfully".to_string(),
        data: Some(serde_json::json!({
            "auth_tokens": auth_tokens
        })),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    log::info!("Token refresh completed successfully for phone: {}", phone);
    Ok(HttpResponse::Ok().json(response))
}

// Helper function to get user ID from request
async fn get_user_id_from_request(req: &HttpRequest, user_service: &Arc<UserService>) -> Result<String, AppError> {
    // First try to get the phone number from the request extensions (set by the auth middleware)
    if let Some(phone) = req.extensions().get::<String>() {
        log::info!("Found phone number in request extensions: {}", phone);

        // Look up the user ID by phone number
        match user_service.get_user_by_phone(phone).await {
            Ok(user) => {
                if let Some(id) = user.id {
                    let user_id = id.to_hex();
                    log::info!("Found user ID for phone number {}: {}", phone, user_id);
                    return Ok(user_id);
                } else {
                    return Err(AppError::NotFoundError("User ID not found for phone number".to_string()));
                }
            },
            Err(e) => {
                log::error!("Failed to get user by phone {}: {}", phone, e);
                return Err(e);
            }
        }
    }

    // If not found in extensions, try to extract it from the JWT token
    if let Some(auth_header) = req.headers().get(actix_web::http::header::AUTHORIZATION) {
        if let Ok(auth_str) = auth_header.to_str() {
            if auth_str.starts_with("Bearer ") {
                let token = &auth_str[7..]; // Remove "Bearer " prefix

                // Try to decode the token
                match crate::middleware::auth::Auth::validate_token(token) {
                    Ok(claims) => {
                        // Get the phone number from the token
                        let phone = claims.phone.clone();
                        log::info!("Found phone number in JWT token: {}", phone);

                        // Look up the user ID by phone number
                        match user_service.get_user_by_phone(&phone).await {
                            Ok(user) => {
                                if let Some(id) = user.id {
                                    let user_id = id.to_hex();
                                    log::info!("Found user ID for phone number {}: {}", phone, user_id);
                                    return Ok(user_id);
                                } else {
                                    return Err(AppError::NotFoundError("User ID not found for phone number".to_string()));
                                }
                            },
                            Err(e) => {
                                log::error!("Failed to get user by phone {}: {}", phone, e);
                                return Err(e);
                            }
                        }
                    }
                    Err(e) => {
                        log::warn!("Failed to decode JWT token: {}", e);
                        // Continue to next fallback
                    }
                }
            }
        }
    }

    // If we get here, we couldn't find the user ID
    Err(AppError::AuthenticationError("User ID not found in request".to_string()))
}

// Helper function to get user ID with multiple fallbacks
// This function tries to get the user ID from:


// Get profile picture by user ID
pub async fn get_profile_picture(
    data: web::Data<UsersControllerState>,
    path: web::Path<String>,
) -> Result<HttpResponse, AppError> {
    let user_id = path.into_inner();

    log::info!("Getting profile picture for user_id: {}", user_id);

    // Get user by ID
    let user = data.user_service.get_user_by_id(&user_id).await?;

    // Check if user has a profile picture
    if let Some(profile_picture) = user.profile_picture {
        let response = ApiResponse {
            success: true,
            message: "Profile picture retrieved successfully".to_string(),
            data: Some(serde_json::json!({
                "profile_picture_url": profile_picture
            })),
            entities: None,
            timestamp: Some(Utc::now().timestamp() as u64),
            duration: None,
            count: None,
            cursor: None,
        };

        return Ok(HttpResponse::Ok().json(response));
    } else {
        return Err(AppError::NotFoundError("User does not have a profile picture".to_string()));
    }
}

// Initialize user data (comprehensive endpoint that returns all user info)
pub async fn init_user(
    data: web::Data<UsersControllerState>,
    query: web::Query<UserQuery>,
    req: HttpRequest,
) -> Result<HttpResponse, AppError> {
    // Check if phone is provided in query parameters
    if let Some(phone) = &query.phone {
        // Clean up the phone number by trimming spaces and ensuring it has a + prefix
        let trimmed_phone = phone.trim();
        let formatted_phone = if !trimmed_phone.starts_with('+') {
            format!("+{}", trimmed_phone)
        } else {
            trimmed_phone.to_string()
        };

        // Log the formatted phone number for debugging
        log::info!("Initializing user with formatted phone: '{}'", formatted_phone);

        // Get user by phone number
        let user = data.user_service.get_user_by_phone(&formatted_phone).await?;

        // Update last seen
        if let Some(id) = &user.id {
            let user_id = id.to_hex();
            data.user_service.update_last_seen(&user_id).await?;
        }



        // Convert to safe user response
        let safe_user = data.user_service.to_safe_user_response(user);

        // Create comprehensive response with all user data
        let response = ApiResponse {
            success: true,
            message: "User initialized successfully".to_string(),
            data: Some(serde_json::json!({
                "user": safe_user
            })),
            entities: None,
            timestamp: Some(Utc::now().timestamp() as u64),
            duration: None,
            count: None,
            cursor: None,
        };

        return Ok(HttpResponse::Ok().json(response));
    }

    // Try to get user ID with fallbacks (from query, JWT token, etc.)
    let user_id = get_user_id_with_fallbacks(
        &req,
        None,
        Some(&query as &dyn std::any::Any),
        None,
        &data.user_service,
    ).await?;

    // Get user
    let user = data.user_service.get_user_by_id(&user_id).await?;

    // Update last seen
    data.user_service.update_last_seen(&user_id).await?;



    // Convert to safe user response
    let safe_user = data.user_service.to_safe_user_response(user);

    // Create comprehensive response with all user data
    let response = ApiResponse {
        success: true,
        message: "User initialized successfully".to_string(),
        data: Some(serde_json::json!({
            "user": safe_user
        })),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// 1. Request body (if provided)
// 2. Query parameters (if provided)
// 3. Path parameters (if provided)
// 4. Request extensions (set by auth middleware)
// 5. JWT token in Authorization header
pub async fn get_user_id_with_fallbacks(
    req: &HttpRequest,
    body_user_id: Option<&str>,
    query_params: Option<&dyn std::any::Any>,
    path_params: Option<&str>,
    user_service: &Arc<UserService>,
) -> Result<String, AppError>
{
    // 1. Try to get user ID from request body
    if let Some(id) = body_user_id {
        if !id.is_empty() {
            log::info!("Using user ID from request body: {}", id);
            return Ok(id.to_string());
        }
    }

    // 2. Try to get user ID from query parameters
    if let Some(query) = query_params {
        // For UserQuery type, we know it has a user_id field
        if let Some(user_query) = query.downcast_ref::<web::Query<UserQuery>>() {
            if let Some(user_id) = &user_query.user_id {
                if !user_id.is_empty() {
                    log::info!("Using user ID from query parameters: {}", user_id);
                    return Ok(user_id.to_string());
                }
            }
        }
    }

    // 3. Try to get user ID from path parameters
    if let Some(id) = path_params {
        if !id.is_empty() {
            log::info!("Using user ID from path parameters: {}", id);
            return Ok(id.to_string());
        }
    }

    // 4 & 5. Fallback to getting user ID from request extensions or JWT token
    get_user_id_from_request(req, user_service).await
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserDetailsQuery {
    pub phone: String,
}

// Get user details
pub async fn get_user_details(
    req: HttpRequest,
    query: web::Query<UserDetailsQuery>,
    data: web::Data<UsersControllerState>,
) -> Result<HttpResponse, AppError> {
    // Verify the user is authenticated
    let _auth_user_id = req.extensions().get::<String>()
        .ok_or_else(|| AppError::AuthenticationError("Authentication required".to_string()))?;

    // Clean up the phone number by trimming spaces and ensuring it has a + prefix
    let trimmed_phone = query.phone.trim();
    let formatted_phone = if !trimmed_phone.starts_with('+') {
        format!("+{}", trimmed_phone)
    } else {
        trimmed_phone.to_string()
    };

    log::info!("Getting user details with phone: '{}'", crate::utils::mask_sensitive_data(&formatted_phone));
    let user = data.user_service.get_user_by_phone(&formatted_phone).await?;

    // Prepare the response data
    let response_data = serde_json::json!({
        "id": user.id.map_or_else(|| "".to_string(), |id| id.to_hex()),
        "username": user.username,
        "phone": user.phone,
        "profile_picture": user.profile_picture,
        "bio": user.bio,
        "address": user.address,
        "status": user.status
    });

    let response = ApiResponse {
        success: true,
        message: "User details retrieved successfully".to_string(),
        data: Some(response_data),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Debug endpoint to test phone lookup
pub async fn debug_phone_lookup(
    data: web::Data<UsersControllerState>,
    query: web::Query<HashMap<String, String>>,
) -> Result<HttpResponse, AppError> {
    if let Some(phone) = query.get("phone") {
        log::info!("Debug: Looking up phone: '{}'", phone);

        // Test the exact phone as provided
        log::info!("Debug: Testing exact phone: '{}'", phone);
        match data.user_service.get_user_by_phone(phone).await {
            Ok(user) => {
                log::info!("Debug: Found user with exact phone");
                return Ok(HttpResponse::Ok().json(json!({
                    "success": true,
                    "message": "User found with exact phone",
                    "data": {
                        "user_id": user.id,
                        "phone": user.phone,
                        "is_verified": user.is_verified,
                        "searched_phone": phone
                    }
                })));
            },
            Err(e) => {
                log::info!("Debug: Exact phone failed: {}", e);
            }
        }

        // Test with formatting logic from login
        let trimmed_phone = phone.trim();
        let formatted_phone = if !trimmed_phone.starts_with('+') {
            format!("+{}", trimmed_phone)
        } else {
            trimmed_phone.to_string()
        };

        log::info!("Debug: Testing formatted phone: '{}'", formatted_phone);
        match data.user_service.get_user_by_phone(&formatted_phone).await {
            Ok(user) => {
                log::info!("Debug: Found user with formatted phone");
                Ok(HttpResponse::Ok().json(json!({
                    "success": true,
                    "message": "User found with formatted phone",
                    "data": {
                        "user_id": user.id,
                        "phone": user.phone,
                        "is_verified": user.is_verified,
                        "original_phone": phone,
                        "formatted_phone": formatted_phone
                    }
                })))
            },
            Err(e) => {
                log::error!("Debug: All phone lookup attempts failed: {}", e);
                Ok(HttpResponse::Ok().json(json!({
                    "success": false,
                    "message": format!("User lookup failed: {}", e),
                    "data": {
                        "original_phone": phone,
                        "formatted_phone": formatted_phone,
                        "trimmed_phone": trimmed_phone
                    }
                })))
            }
        }
    } else {
        Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "message": "Phone parameter required. Usage: /debug-phone?phone=+1234567890"
        })))
    }
}

// Debug endpoint to test Twilio credentials
pub async fn debug_twilio_test(
    data: web::Data<UsersControllerState>,
) -> Result<HttpResponse, AppError> {
    log::info!("Testing Twilio credentials...");

    match data.twilio_service.test_credentials().await {
        Ok(true) => {
            log::info!("Twilio credentials test: SUCCESS");
            Ok(HttpResponse::Ok().json(json!({
                "success": true,
                "message": "Twilio credentials are valid",
                "data": {
                    "credentials_valid": true
                }
            })))
        },
        Ok(false) => {
            log::warn!("Twilio credentials test: FAILED");
            Ok(HttpResponse::Ok().json(json!({
                "success": false,
                "message": "Twilio credentials are invalid",
                "data": {
                    "credentials_valid": false
                }
            })))
        },
        Err(e) => {
            log::error!("Twilio credentials test: ERROR - {}", e);
            Ok(HttpResponse::Ok().json(json!({
                "success": false,
                "message": format!("Twilio test failed: {}", e),
                "data": {
                    "credentials_valid": false,
                    "error": e.to_string()
                }
            })))
        }
    }
}

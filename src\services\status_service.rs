use chrono::{Duration, Utc};
use mongodb::{
    bson::{doc, oid::ObjectId, Document},
    options::{FindOptions, UpdateOptions},
    Collection,
};
use std::sync::Arc;

use crate::errors::AppError;
use crate::models::{
    Status, StatusView, StatusPrivacySettings, StatusResponse, StatusViewResponse,
    StatusSummary, StatusContentType, StatusPrivacy, CreateStatusRequest,
    UpdateStatusPrivacyRequest, User, StatusUpdateMessage
};
use crate::services::mongodb::MongoDBService;
use crate::services::ContactService;

pub struct StatusService {
    mongodb: Arc<MongoDBService>,
    status_collection: Collection<Status>,
    privacy_collection: Collection<StatusPrivacySettings>,
    users_collection: Collection<User>,
    contact_service: Option<Arc<ContactService>>, // Optional to avoid circular dependency
}

impl StatusService {
    pub fn new(mongodb: Arc<MongoDBService>) -> Self {
        let status_collection = mongodb.collection::<Status>("status");
        let privacy_collection = mongodb.collection::<StatusPrivacySettings>("status_privacy");
        let users_collection = mongodb.collection::<User>("users");

        Self {
            mongodb,
            status_collection,
            privacy_collection,
            users_collection,
            contact_service: None,
        }
    }

    // Set contact service after initialization to avoid circular dependency
    pub fn set_contact_service(&mut self, contact_service: Arc<ContactService>) {
        self.contact_service = Some(contact_service);
    }

    // Create a new status
    pub async fn create_status(&self, user_id: &str, request: CreateStatusRequest) -> Result<Status, AppError> {
        let now = Utc::now();
        let expires_at = now + Duration::hours(24); // 24-hour expiration

        let privacy_settings = self.get_privacy_settings(user_id).await?;
        let privacy_setting = request.privacy_setting.unwrap_or(privacy_settings.default_privacy);

        // Handle allowed/blocked contacts based on privacy setting
        let (allowed_contacts, blocked_contacts) = match privacy_setting {
            StatusPrivacy::Everyone => (Vec::new(), privacy_settings.always_block),
            StatusPrivacy::Contacts => (Vec::new(), privacy_settings.always_block),
            StatusPrivacy::ContactsExcept => (
                Vec::new(), 
                [privacy_settings.always_block, request.blocked_contacts.unwrap_or_default()].concat()
            ),
            StatusPrivacy::OnlyShare => (
                request.allowed_contacts.unwrap_or_default(),
                privacy_settings.always_block
            ),
        };

        // Handle content URL based on content type
        let content_url = match request.content_type {
            StatusContentType::Text => {
                // For text status, use the content field as the content_url
                request.content.unwrap_or_default()
            },
            StatusContentType::Image | StatusContentType::Video => {
                // For media status, use the content_url field
                request.content_url.unwrap_or_default()
            }
        };

        let status = Status {
            id: None,
            user_id: user_id.to_string(),
            content_type: request.content_type,
            content_url,
            caption: request.caption,
            background_color: request.background_color,
            text_color: request.text_color,
            font_style: request.font_style,
            privacy_setting,
            allowed_contacts,
            blocked_contacts,
            created_at_millis: now.timestamp_millis(),
            expires_at_millis: expires_at.timestamp_millis(),
            is_active: true,
            view_count: 0,
            views: Vec::new(),
        };

        let result = self.status_collection.insert_one(status.clone(), None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create status: {}", e)))?;

        let id = result.inserted_id.as_object_id()
            .ok_or_else(|| AppError::DatabaseError("Failed to get inserted status ID".to_string()))?;

        let filter = doc! { "_id": id };
        let created_status = self.status_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get created status: {}", e)))?;

        match created_status {
            Some(status) => Ok(status),
            None => Err(AppError::DatabaseError("Failed to get created status".to_string())),
        }
    }

    // Get active statuses for a user's contacts using contact service
    pub async fn get_contact_statuses_from_contacts(&self, user_id: &str) -> Result<Vec<StatusSummary>, AppError> {
        if let Some(contact_service) = &self.contact_service {
            // Get registered contacts for this user
            let registered_contact_ids = contact_service.get_registered_contacts(user_id).await?;

            // Use the existing method with the registered contact IDs
            let mut summaries = self.get_contact_statuses(user_id, &registered_contact_ids).await?;

            // Enrich each summary with contact information
            for summary in &mut summaries {
                if let Err(e) = self.enrich_status_summary_with_contacts(summary, user_id).await {
                    log::warn!("Failed to enrich status summary for user {}: {}", summary.user_id, e);
                }
            }

            Ok(summaries)
        } else {
            // Fallback to empty result if contact service not available
            Ok(Vec::new())
        }
    }

    // Get active statuses for a user's contacts (legacy method)
    pub async fn get_contact_statuses(&self, user_id: &str, contact_ids: &[String]) -> Result<Vec<StatusSummary>, AppError> {
        let now = Utc::now().timestamp_millis();
        
        // aggregation pipeline to get status summaries
        let pipeline = vec![
            doc! {
                "$match": {
                    "user_id": { "$in": contact_ids },
                    "is_active": true,
                    "expires_at_millis": { "$gt": now },
                    "$or": [
                        { "privacy_setting": "Everyone" },
                        { "privacy_setting": "Contacts" },
                        { 
                            "privacy_setting": "ContactsExcept",
                            "blocked_contacts": { "$ne": user_id }
                        },
                        {
                            "privacy_setting": "OnlyShare",
                            "allowed_contacts": user_id
                        }
                    ]
                }
            },
            doc! {
                "$group": {
                    "_id": "$user_id",
                    "status_count": { "$sum": 1 },
                    "latest_status_time": { "$max": "$created_at_millis" },
                    "has_unviewed": {
                        "$sum": {
                            "$cond": [
                                { "$not": { "$in": [user_id, "$views.viewer_id"] } },
                                1,
                                0
                            ]
                        }
                    }
                }
            }
        ];

        let mut cursor = self.status_collection.aggregate(pipeline, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get contact statuses: {}", e)))?;

        let mut summaries = Vec::new();
        while cursor.advance().await.map_err(|e| AppError::DatabaseError(format!("Failed to iterate statuses: {}", e)))? {
            let doc = cursor.current();
            
            let user_id = doc.get_str("_id").unwrap_or_default().to_string();
            let status_count = doc.get_i32("status_count").unwrap_or(0);
            let latest_status_time = doc.get_i64("latest_status_time").unwrap_or(0);
            let has_unviewed = doc.get_i32("has_unviewed").unwrap_or(0) > 0;

            // Get user info
            let user = self.get_user_info(&user_id).await?;

            summaries.push(StatusSummary {
                user_id,
                username: user.username,
                display_name: None, // Will be populated by enrichment
                phone_number: None, // Will be populated by enrichment
                profile_picture: user.profile_picture,
                status_count,
                latest_status_time,
                has_unviewed,
                is_contact: false, // Will be populated by enrichment
                last_seen_millis: None, // Will be populated by enrichment
            });
        }

        Ok(summaries)
    }

    // Get statuses for a specific user with contact enrichment
    pub async fn get_user_statuses_enriched(&self, user_id: &str, viewer_id: &str) -> Result<Vec<StatusResponse>, AppError> {
        let mut statuses = self.get_user_statuses(user_id, viewer_id).await?;

        // Enrich each status with contact information
        for status in &mut statuses {
            if let Err(e) = self.enrich_status_response_with_contacts(status, viewer_id).await {
                log::warn!("Failed to enrich status response for user {}: {}", status.user_id, e);
            }
        }

        Ok(statuses)
    }

    // Get statuses for a specific user (legacy method)
    pub async fn get_user_statuses(&self, user_id: &str, viewer_id: &str) -> Result<Vec<StatusResponse>, AppError> {
        let now = Utc::now().timestamp_millis();
        
        let filter = doc! {
            "user_id": user_id,
            "is_active": true,
            "expires_at_millis": { "$gt": now }
        };

        let options = FindOptions::builder()
            .sort(doc! { "created_at_millis": 1 })
            .build();

        let mut cursor = self.status_collection.find(filter, options).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get user statuses: {}", e)))?;

        let mut statuses = Vec::new();
        while cursor.advance().await.map_err(|e| AppError::DatabaseError(format!("Failed to iterate statuses: {}", e)))? {
            let status: Status = cursor.deserialize_current()
                .map_err(|e| AppError::DatabaseError(format!("Failed to deserialize status: {}", e)))?;

            if !self.can_view_status(&status, viewer_id).await? {
                continue;
            }

            let user = self.get_user_info(&status.user_id).await?;

            let has_viewed = status.views.iter().any(|view| view.viewer_id == viewer_id);

            let mut status_response = StatusResponse::from(status);
            status_response.username = user.username;
            status_response.profile_picture = user.profile_picture;
            status_response.has_viewed = has_viewed;
            status_response.can_view = true;

            statuses.push(status_response);
        }

        Ok(statuses)
    }

    // View a status (mark as viewed)
    pub async fn view_status(&self, status_id: &str, viewer_id: &str) -> Result<(), AppError> {
        let object_id = ObjectId::parse_str(status_id)
            .map_err(|_| AppError::ValidationError(format!("Invalid status ID: {}", status_id)))?;

        let filter = doc! { "_id": object_id };
        let status = self.status_collection.find_one(filter.clone(), None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get status: {}", e)))?;

        let status = match status {
            Some(status) => status,
            None => return Err(AppError::NotFoundError(format!("Status not found with ID: {}", status_id))),
        };

        if !self.can_view_status(&status, viewer_id).await? {
            return Err(AppError::AuthenticationError("You cannot view this status".to_string()));
        }

        if status.views.iter().any(|view| view.viewer_id == viewer_id) {
            return Ok(()); 
        }

        let viewer = self.get_user_info(viewer_id).await?;

        let view = StatusView {
            viewer_id: viewer_id.to_string(),
            viewed_at_millis: Utc::now().timestamp_millis(),
            viewer_username: viewer.username,
            viewer_profile_picture: viewer.profile_picture,
        };

        let update = doc! {
            "$push": { "views": mongodb::bson::to_bson(&view).unwrap() },
            "$inc": { "view_count": 1 }
        };

        self.status_collection.update_one(filter, update, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to update status views: {}", e)))?;

        Ok(())
    }

    // Get status views (who viewed the status)
    pub async fn get_status_views(&self, status_id: &str, owner_id: &str) -> Result<Vec<StatusViewResponse>, AppError> {
        let object_id = ObjectId::parse_str(status_id)
            .map_err(|_| AppError::ValidationError(format!("Invalid status ID: {}", status_id)))?;

        let filter = doc! { "_id": object_id, "user_id": owner_id };
        let status = self.status_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get status: {}", e)))?;

        let status = match status {
            Some(status) => status,
            None => return Err(AppError::NotFoundError(format!("Status not found with ID: {}", status_id))),
        };

        let privacy_settings = self.get_privacy_settings(owner_id).await?;
        if !privacy_settings.read_receipts_enabled {
            return Ok(Vec::new()); 
        }

        let views: Vec<StatusViewResponse> = status.views.into_iter()
            .map(|view| StatusViewResponse {
                viewer_id: view.viewer_id,
                viewer_username: view.viewer_username,
                viewer_profile_picture: view.viewer_profile_picture,
                viewed_at_millis: view.viewed_at_millis,
            })
            .collect();

        Ok(views)
    }


    async fn can_view_status(&self, status: &Status, viewer_id: &str) -> Result<bool, AppError> {
        // Owner can always view their own status
        if status.user_id == viewer_id {
            return Ok(true);
        }

        if status.blocked_contacts.contains(&viewer_id.to_string()) {
            return Ok(false);
        }

        match status.privacy_setting {
            StatusPrivacy::Everyone => Ok(true),
            StatusPrivacy::Contacts => {
                self.are_contacts(&status.user_id, viewer_id).await
            },
            StatusPrivacy::ContactsExcept => {

                self.are_contacts(&status.user_id, viewer_id).await
            },
            StatusPrivacy::OnlyShare => {
                Ok(status.allowed_contacts.contains(&viewer_id.to_string()))
            },
        }
    }

    async fn are_contacts(&self, user_id: &str, contact_id: &str) -> Result<bool, AppError> {
        let filter = doc! { 
            "_id": ObjectId::parse_str(user_id).map_err(|_| AppError::ValidationError("Invalid user ID".to_string()))?,
            "contacts": contact_id
        };
        
        let user = self.users_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to check contacts: {}", e)))?;

        Ok(user.is_some())
    }

    async fn get_user_info(&self, user_id: &str) -> Result<User, AppError> {
        let object_id = ObjectId::parse_str(user_id)
            .map_err(|_| AppError::ValidationError(format!("Invalid user ID: {}", user_id)))?;

        let filter = doc! { "_id": object_id };
        let user = self.users_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get user: {}", e)))?;

        match user {
            Some(user) => Ok(user),
            None => Err(AppError::NotFoundError(format!("User not found with ID: {}", user_id))),
        }
    }

    async fn get_privacy_settings(&self, user_id: &str) -> Result<StatusPrivacySettings, AppError> {
        let filter = doc! { "user_id": user_id };
        let settings = self.privacy_collection.find_one(filter.clone(), None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get privacy settings: {}", e)))?;

        match settings {
            Some(settings) => Ok(settings),
            None => {
                let now = Utc::now().timestamp_millis();
                let default_settings = StatusPrivacySettings {
                    id: None,
                    user_id: user_id.to_string(),
                    default_privacy: StatusPrivacy::Contacts,
                    always_allow: Vec::new(),
                    always_block: Vec::new(),
                    read_receipts_enabled: true,
                    created_at_millis: now,
                    updated_at_millis: now,
                };

                self.privacy_collection.insert_one(default_settings.clone(), None).await
                    .map_err(|e| AppError::DatabaseError(format!("Failed to create privacy settings: {}", e)))?;

                Ok(default_settings)
            }
        }
    }

    // Update privacy settings
    pub async fn update_privacy_settings(&self, user_id: &str, request: UpdateStatusPrivacyRequest) -> Result<StatusPrivacySettings, AppError> {
        let filter = doc! { "user_id": user_id };

        let mut update_doc = Document::new();
        let mut set_doc = Document::new();

        if let Some(default_privacy) = request.default_privacy {
            set_doc.insert("default_privacy", mongodb::bson::to_bson(&default_privacy).unwrap());
        }

        if let Some(always_allow) = request.always_allow {
            set_doc.insert("always_allow", always_allow);
        }

        if let Some(always_block) = request.always_block {
            set_doc.insert("always_block", always_block);
        }

        if let Some(read_receipts_enabled) = request.read_receipts_enabled {
            set_doc.insert("read_receipts_enabled", read_receipts_enabled);
        }

        set_doc.insert("updated_at_millis", Utc::now().timestamp_millis());
        update_doc.insert("$set", set_doc);

        let options = UpdateOptions::builder()
            .upsert(true)
            .build();

        self.privacy_collection.update_one(filter.clone(), update_doc, options).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to update privacy settings: {}", e)))?;

        // Return updated settings
        self.get_privacy_settings(user_id).await
    }

    // Delete a status
    pub async fn delete_status(&self, status_id: &str, user_id: &str) -> Result<(), AppError> {
        let object_id = ObjectId::parse_str(status_id)
            .map_err(|_| AppError::ValidationError(format!("Invalid status ID: {}", status_id)))?;

        let filter = doc! { "_id": object_id, "user_id": user_id };

        let result = self.status_collection.delete_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to delete status: {}", e)))?;

        if result.deleted_count == 0 {
            return Err(AppError::NotFoundError(format!("Status not found with ID: {}", status_id)));
        }

        Ok(())
    }

    // Get my statuses
    pub async fn get_my_statuses(&self, user_id: &str) -> Result<Vec<StatusResponse>, AppError> {
        let now = Utc::now().timestamp_millis();

        let filter = doc! {
            "user_id": user_id,
            "is_active": true,
            "expires_at_millis": { "$gt": now }
        };

        let options = FindOptions::builder()
            .sort(doc! { "created_at_millis": -1 })
            .build();

        let mut cursor = self.status_collection.find(filter, options).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get my statuses: {}", e)))?;

        let mut statuses = Vec::new();
        while cursor.advance().await.map_err(|e| AppError::DatabaseError(format!("Failed to iterate statuses: {}", e)))? {
            let status: Status = cursor.deserialize_current()
                .map_err(|e| AppError::DatabaseError(format!("Failed to deserialize status: {}", e)))?;

            // Get user info
            let user = self.get_user_info(&status.user_id).await?;

            let mut status_response = StatusResponse::from(status);
            status_response.username = user.username;
            status_response.profile_picture = user.profile_picture;
            status_response.has_viewed = true; // Owner has always "viewed" their own status
            status_response.can_view = true;
            status_response.is_contact = false; // User is not a contact of themselves
            status_response.last_seen_millis = Some(user.last_seen_millis);

            statuses.push(status_response);
        }

        Ok(statuses)
    }

    // Clean up expired statuses (background job)
    pub async fn cleanup_expired_statuses(&self) -> Result<u64, AppError> {
        let now = Utc::now().timestamp_millis();

        let filter = doc! {
            "$or": [
                { "expires_at_millis": { "$lt": now } },
                { "is_active": false }
            ]
        };

        let result = self.status_collection.delete_many(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to cleanup expired statuses: {}", e)))?;

        Ok(result.deleted_count)
    }

    // Public method to get privacy settings (used by controller)
    pub async fn get_user_privacy_settings(&self, user_id: &str) -> Result<StatusPrivacySettings, AppError> {
        self.get_privacy_settings(user_id).await
    }

    // Create status update message for WebSocket broadcasting
    pub fn create_status_update_message(&self, status: &Status, message_type: &str) -> StatusUpdateMessage {
        StatusUpdateMessage {
            message_type: message_type.to_string(),
            user_id: status.user_id.clone(),
            status_id: status.id.as_ref().map(|id| id.to_hex()),
            content_type: Some(match status.content_type {
                StatusContentType::Text => "text".to_string(),
                StatusContentType::Image => "image".to_string(),
                StatusContentType::Video => "video".to_string(),
            }),
            content_url: Some(status.content_url.clone()),
            caption: status.caption.clone(),
            created_at_millis: Some(status.created_at_millis),
            expires_at_millis: Some(status.expires_at_millis),
            is_active: Some(status.is_active),
        }
    }

    // Enrich status summary with contact information
    pub async fn enrich_status_summary_with_contacts(&self, summary: &mut StatusSummary, viewer_id: &str) -> Result<(), AppError> {
        if let Some(contact_service) = &self.contact_service {
            // Get contact information for this user
            if let Ok(contacts) = contact_service.get_user_contacts(viewer_id).await {
                if let Some(contact) = contacts.contacts.iter().find(|c| c.registered_user_id.as_ref() == Some(&summary.user_id)) {
                    summary.display_name = Some(contact.display_name.clone());
                    summary.phone_number = Some(contact.phone_number.clone());
                    summary.is_contact = true;
                } else {
                    summary.is_contact = false;
                }
            }
        }

        // Get user information
        if let Ok(Some(user)) = self.get_user_by_id(&summary.user_id).await {
            summary.last_seen_millis = Some(user.last_seen_millis);
            if summary.username.is_none() {
                summary.username = user.username;
            }
            if summary.profile_picture.is_none() {
                summary.profile_picture = user.profile_picture;
            }
        }

        Ok(())
    }

    // Enrich status response with contact information
    pub async fn enrich_status_response_with_contacts(&self, response: &mut StatusResponse, viewer_id: &str) -> Result<(), AppError> {
        if let Some(contact_service) = &self.contact_service {
            // Get contact information for this user
            if let Ok(contacts) = contact_service.get_user_contacts(viewer_id).await {
                if let Some(contact) = contacts.contacts.iter().find(|c| c.registered_user_id.as_ref() == Some(&response.user_id)) {
                    response.display_name = Some(contact.display_name.clone());
                    response.phone_number = Some(contact.phone_number.clone());
                    response.is_contact = true;
                } else {
                    response.is_contact = false;
                }
            }
        }

        // Get user information
        if let Ok(Some(user)) = self.get_user_by_id(&response.user_id).await {
            response.last_seen_millis = Some(user.last_seen_millis);
            if response.username.is_none() {
                response.username = user.username;
            }
            if response.profile_picture.is_none() {
                response.profile_picture = user.profile_picture;
            }
        }

        Ok(())
    }

    // Helper method to get user by ID
    async fn get_user_by_id(&self, user_id: &str) -> Result<Option<User>, AppError> {
        let object_id = ObjectId::parse_str(user_id)
            .map_err(|_| AppError::ValidationError("Invalid user ID format".to_string()))?;

        let filter = doc! { "_id": object_id };

        self.users_collection
            .find_one(filter, None)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get user: {}", e)))
    }
}

use chrono::Utc;

use mongodb::{
    bson::{doc, <PERSON><PERSON>},
    options::FindOneAndUpdateOptions,
    Collection,
};
use std::sync::Arc;

use crate::errors::AppError;
use crate::models::{
    UserSettings, NotificationSettings, PrivacySettings, AppearanceSettings
};
use crate::services::mongodb::MongoDBService;

pub struct UserSettingsService {
    mongodb: Arc<MongoDBService>,
    settings_collection: Collection<UserSettings>,
}

impl UserSettingsService {
    pub fn new(mongodb: Arc<MongoDBService>) -> Self {
        let settings_collection = mongodb.collection::<UserSettings>("user_settings");

        Self {
            mongodb,
            settings_collection,
        }
    }

    // Get user settings
    pub async fn get_user_settings(&self, user_id: &str) -> Result<UserSettings, AppError> {
        let filter = doc! { "user_id": user_id };
        let settings = self.settings_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get user settings: {}", e)))?;

        match settings {
            Some(settings) => Ok(settings),
            None => {
                // Create default settings if none exist
                self.create_default_settings(user_id).await
            }
        }
    }

    // Create default settings
    pub async fn create_default_settings(&self, user_id: &str) -> Result<UserSettings, AppError> {
        let now = Utc::now();

        let settings = UserSettings {
            id: None,
            user_id: user_id.to_string(),
            notifications: NotificationSettings {
                message_notifications: true,
                call_notifications: true,
                group_notifications: true,
                in_app_sounds: true,
                in_app_vibrations: true,
                show_previews: true,
            },
            privacy: PrivacySettings {
                last_seen: "everyone".to_string(),
                profile_photo: "everyone".to_string(),
                status: "everyone".to_string(),
                read_receipts: true,
                groups: "everyone".to_string(),
                blocked_contacts: Vec::new(),
            },
            appearance: AppearanceSettings {
                theme: "system".to_string(),
                chat_wallpaper: "default".to_string(),
                font_size: "medium".to_string(),
            },
            language: "en".to_string(),
            created_at: now.clone(),
            updated_at: now,
        };

        let result = self.settings_collection.insert_one(settings.clone(), None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create user settings: {}", e)))?;

        // Get the inserted settings
        let id = result.inserted_id.as_object_id()
            .ok_or_else(|| AppError::DatabaseError("Failed to get inserted settings ID".to_string()))?;

        let filter = doc! { "_id": id };
        let created_settings = self.settings_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get created settings: {}", e)))?;

        match created_settings {
            Some(settings) => Ok(settings),
            None => Err(AppError::DatabaseError("Failed to get created settings".to_string())),
        }
    }

    // Update notification settings
    pub async fn update_notification_settings(&self, user_id: &str, settings: NotificationSettings) -> Result<UserSettings, AppError> {
        let filter = doc! { "user_id": user_id };
        let now = Utc::now();
        let now_bson = Bson::DateTime(mongodb::bson::DateTime::from_millis(now.timestamp_millis()));

        let update = doc! {
            "$set": {
                "notifications": {
                    "message_notifications": settings.message_notifications,
                    "call_notifications": settings.call_notifications,
                    "group_notifications": settings.group_notifications,
                    "in_app_sounds": settings.in_app_sounds,
                    "in_app_vibrations": settings.in_app_vibrations,
                    "show_previews": settings.show_previews
                },
                "updated_at": now_bson
            }
        };

        let options = FindOneAndUpdateOptions::builder()
            .return_document(mongodb::options::ReturnDocument::After)
            .upsert(true)
            .build();

        let updated_settings = self.settings_collection.find_one_and_update(filter, update, options).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to update notification settings: {}", e)))?;

        match updated_settings {
            Some(settings) => Ok(settings),
            None => Err(AppError::DatabaseError("Failed to update notification settings".to_string())),
        }
    }

    // Update privacy settings
    pub async fn update_privacy_settings(&self, user_id: &str, settings: PrivacySettings) -> Result<UserSettings, AppError> {
        let filter = doc! { "user_id": user_id };
        let now = Utc::now();
        let now_bson = Bson::DateTime(mongodb::bson::DateTime::from_millis(now.timestamp_millis()));

        let update = doc! {
            "$set": {
                "privacy": {
                    "last_seen": settings.last_seen,
                    "profile_photo": settings.profile_photo,
                    "status": settings.status,
                    "read_receipts": settings.read_receipts,
                    "groups": settings.groups,
                    "blocked_contacts": settings.blocked_contacts
                },
                "updated_at": now_bson
            }
        };

        let options = FindOneAndUpdateOptions::builder()
            .return_document(mongodb::options::ReturnDocument::After)
            .upsert(true)
            .build();

        let updated_settings = self.settings_collection.find_one_and_update(filter, update, options).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to update privacy settings: {}", e)))?;

        match updated_settings {
            Some(settings) => Ok(settings),
            None => Err(AppError::DatabaseError("Failed to update privacy settings".to_string())),
        }
    }

    // Update appearance settings
    pub async fn update_appearance_settings(&self, user_id: &str, settings: AppearanceSettings) -> Result<UserSettings, AppError> {
        let filter = doc! { "user_id": user_id };
        let now = Utc::now();
        let now_bson = Bson::DateTime(mongodb::bson::DateTime::from_millis(now.timestamp_millis()));

        let update = doc! {
            "$set": {
                "appearance": {
                    "theme": settings.theme,
                    "chat_wallpaper": settings.chat_wallpaper,
                    "font_size": settings.font_size
                },
                "updated_at": now_bson
            }
        };

        let options = FindOneAndUpdateOptions::builder()
            .return_document(mongodb::options::ReturnDocument::After)
            .upsert(true)
            .build();

        let updated_settings = self.settings_collection.find_one_and_update(filter, update, options).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to update appearance settings: {}", e)))?;

        match updated_settings {
            Some(settings) => Ok(settings),
            None => Err(AppError::DatabaseError("Failed to update appearance settings".to_string())),
        }
    }

    // Update language
    pub async fn update_language(&self, user_id: &str, language: &str) -> Result<UserSettings, AppError> {
        let filter = doc! { "user_id": user_id };
        let now = Utc::now();
        let now_bson = Bson::DateTime(mongodb::bson::DateTime::from_millis(now.timestamp_millis()));

        let update = doc! {
            "$set": {
                "language": language,
                "updated_at": now_bson
            }
        };

        let options = FindOneAndUpdateOptions::builder()
            .return_document(mongodb::options::ReturnDocument::After)
            .upsert(true)
            .build();

        let updated_settings = self.settings_collection.find_one_and_update(filter, update, options).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to update language: {}", e)))?;

        match updated_settings {
            Some(settings) => Ok(settings),
            None => Err(AppError::DatabaseError("Failed to update language".to_string())),
        }
    }

    // Block a contact
    pub async fn block_contact(&self, user_id: &str, contact_id: &str) -> Result<(), AppError> {
        let filter = doc! { "user_id": user_id };
        let now = Utc::now();
        let now_bson = Bson::DateTime(mongodb::bson::DateTime::from_millis(now.timestamp_millis()));

        let update = doc! {
            "$addToSet": {
                "privacy.blocked_contacts": contact_id
            },
            "$set": {
                "updated_at": now_bson
            }
        };

        self.settings_collection.update_one(filter, update, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to block contact: {}", e)))?;

        Ok(())
    }

    // Delete user settings
    pub async fn delete_user_settings(&self, user_id: &str) -> Result<(), AppError> {
        let filter = doc! { "user_id": user_id };

        // Delete the user settings from the database
        let result = self.settings_collection.delete_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to delete user settings: {}", e)))?;

        if result.deleted_count == 0 {
            log::warn!("No settings found to delete for user ID: {}", user_id);
        }

        Ok(())
    }

    // Unblock a contact
    pub async fn unblock_contact(&self, user_id: &str, contact_id: &str) -> Result<(), AppError> {
        let filter = doc! { "user_id": user_id };
        let now = Utc::now();
        let now_bson = Bson::DateTime(mongodb::bson::DateTime::from_millis(now.timestamp_millis()));

        let update = doc! {
            "$pull": {
                "privacy.blocked_contacts": contact_id
            },
            "$set": {
                "updated_at": now_bson
            }
        };

        self.settings_collection.update_one(filter, update, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to unblock contact: {}", e)))?;

        Ok(())
    }

    // Get blocked contacts
    pub async fn get_blocked_contacts(&self, user_id: &str) -> Result<Vec<String>, AppError> {
        let filter = doc! { "user_id": user_id };
        let settings = self.settings_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get user settings: {}", e)))?;

        match settings {
            Some(settings) => Ok(settings.privacy.blocked_contacts),
            None => Ok(Vec::new()),
        }
    }
}

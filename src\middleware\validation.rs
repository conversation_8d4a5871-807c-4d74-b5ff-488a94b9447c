use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    error::Error,
    web,
};
use futures::future::{ok, Ready};
use log::warn;
use serde_json::Value;
use std::future::Future;
use std::pin::Pin;
// use std::task::{Context, Poll};
use regex::Regex;
use once_cell::sync::Lazy;

// use crate::errors::AppError;

// Regular expressions for common input validation
static EMAIL_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$").unwrap()
});

static PHONE_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"^\+?[0-9]{10,15}$").unwrap()
});

// SQL Injection patterns to check for
static SQL_INJECTION_PATTERNS: Lazy<Vec<&'static str>> = Lazy::new(|| {
    vec![
        "DROP TABLE",
        "DELETE FROM",
        "INSERT INTO",
        "UPDATE.*SET",
        "--",
        "UNION.*SELECT",
        "SELECT.*FROM",
        "EXEC(",
        "EXECUTE(",
        "WAITFOR",
        "1=1",
        "OR 1=1",
    ]
});

// XSS patterns to check for
static XSS_PATTERNS: Lazy<Vec<&'static str>> = Lazy::new(|| {
    vec![
        "<script>",
        "</script>",
        "javascript:",
        "onerror=",
        "onload=",
        "onclick=",
        "onmouseover=",
        "eval(",
        "document.cookie",
        "alert(",
    ]
});

pub struct InputValidation;

impl InputValidation {
    // Validate email format
    pub fn validate_email(email: &str) -> bool {
        EMAIL_REGEX.is_match(email)
    }

    // Validate phone number format
    pub fn validate_phone(phone: &str) -> bool {
        PHONE_REGEX.is_match(phone)
    }

    // Check for SQL injection patterns
    pub fn check_sql_injection(input: &str) -> bool {
        for pattern in SQL_INJECTION_PATTERNS.iter() {
            if input.to_uppercase().contains(&pattern.to_uppercase()) {
                return true;
            }
        }
        false
    }

    // Check for XSS patterns
    pub fn check_xss(input: &str) -> bool {
        for pattern in XSS_PATTERNS.iter() {
            if input.to_lowercase().contains(&pattern.to_lowercase()) {
                return true;
            }
        }
        false
    }

    // Sanitize input by removing potentially dangerous characters
    pub fn sanitize_input(input: &str) -> String {
        // Replace < and > with their HTML entities
        let sanitized = input
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("\"", "&quot;")
            .replace("'", "&#39;")
            .replace("&", "&amp;");

        sanitized
    }
}

pub struct InputValidationMiddleware;

impl<S, B> Transform<S, ServiceRequest> for InputValidationMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static + Clone,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = InputValidationService<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ok(InputValidationService { service })
    }
}

pub struct InputValidationService<S> {
    service: S,
}

impl<S, B> Service<ServiceRequest> for InputValidationService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + Clone + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>>>>;

    forward_ready!(service);

    fn call(&self, mut req: ServiceRequest) -> Self::Future {
        // Only validate POST, PUT, and PATCH requests
        if req.method() == "POST" || req.method() == "PUT" || req.method() == "PATCH" {
            // Clone the request payload for validation
            let payload = req.extract::<web::Json<Value>>();

            let fut = self.service.clone();

            return Box::pin(async move {
                // Extract the JSON payload
                match payload.await {
                    Ok(json) => {
                        // Validate the JSON payload
                        if let Some(obj) = json.as_object() {
                            for (key, value) in obj {
                                if let Some(str_value) = value.as_str() {
                                    // Check for SQL injection
                                    if InputValidation::check_sql_injection(str_value) {
                                        warn!("Potential SQL injection detected in field {}: {}", key, str_value);
                                        return Err(actix_web::error::ErrorBadRequest("Invalid input detected"));
                                    }

                                    // Check for XSS
                                    if InputValidation::check_xss(str_value) {
                                        warn!("Potential XSS attack detected in field {}: {}", key, str_value);
                                        return Err(actix_web::error::ErrorBadRequest("Invalid input detected"));
                                    }

                                    // Validate email fields
                                    if key.contains("email") && !InputValidation::validate_email(str_value) {
                                        return Err(actix_web::error::ErrorBadRequest("Invalid email format"));
                                    }

                                    // Validate phone fields
                                    if key.contains("phone") && !InputValidation::validate_phone(str_value) {
                                        return Err(actix_web::error::ErrorBadRequest("Invalid phone number format"));
                                    }
                                }
                            }
                        }

                        // Continue with the request
                        let fut = fut.call(req);
                        fut.await
                    },
                    Err(_) => {
                        // If we can't extract the payload, just continue with the request
                        let fut = fut.call(req);
                        fut.await
                    }
                }
            });
        }

        // For other methods, just pass through
        let fut = self.service.call(req);
        Box::pin(async move {
            let res = fut.await?;
            Ok(res)
        })
    }
}

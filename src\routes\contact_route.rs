use actix_web::web;
use crate::controllers::contact_controller;

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/contacts")
            // Upload contacts from device
            .route("/upload", web::post().to(contact_controller::upload_contacts))
            
            // Get user's contacts
            .route("", web::get().to(contact_controller::get_contacts))
            
            // Sync contacts (re-check registration status)
            .route("/sync", web::post().to(contact_controller::sync_contacts))
            
            // WebSocket endpoint for real-time status updates
            .route("/ws", web::get().to(contact_controller::websocket_handler))
            
            // WebSocket connection statistics (for debugging)
            .route("/ws/stats", web::get().to(contact_controller::websocket_stats))
            
            // Test broadcast endpoint (for development/testing)
            .route("/test-broadcast", web::post().to(contact_controller::test_broadcast))
    );
}

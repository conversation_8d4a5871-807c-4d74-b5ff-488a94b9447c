use crate::errors::AppError;
use actix_multipart::Field;
use futures::StreamExt;
use log::{error, info};
use std::fs;
use std::io::Write;
use std::path::{Path, PathBuf};
use uuid::Uuid;
use mime::Mime;

pub struct TempStorageService {
    temp_dir: String,
}

impl TempStorageService {
    pub fn new() -> Self {
        // Create temp directory if it doesn't exist
        let temp_dir = "temp/uploads";

        info!("Initializing TempStorageService with directory: {}", temp_dir);

        // Check if directory exists
        if !Path::new(temp_dir).exists() {
            info!("Temp directory does not exist, creating it now");
            match fs::create_dir_all(temp_dir) {
                Ok(_) => info!("Successfully created temp directory: {}", temp_dir),
                Err(e) => {
                    error!("Failed to create temp directory: {}", e);
                    // Try to create just the temp directory first
                    match fs::create_dir("temp") {
                        Ok(_) => {
                            info!("Created 'temp' directory, now trying to create 'uploads'");
                            match fs::create_dir("temp/uploads") {
                                Ok(_) => info!("Successfully created temp/uploads directory"),
                                Err(e) => error!("Failed to create temp/uploads directory: {}", e),
                            }
                        },
                        Err(e) => error!("Failed to create even the 'temp' directory: {}", e),
                    }
                }
            }
        } else {
            info!("Temp directory already exists: {}", temp_dir);
        }

        Self {
            temp_dir: temp_dir.to_string(),
        }
    }

    // Store a file from a multipart field
    pub async fn store_file(&self, mut field: Field) -> Result<(PathBuf, Option<Mime>), AppError> {
        // Generate a unique filename
        let filename = format!("{}.tmp", Uuid::new_v4());
        let filepath = Path::new(&self.temp_dir).join(&filename);

        info!("Storing multipart field '{}' to temp file: {:?}", field.name(), filepath);

        // Get content type
        let content_type = field.content_type().cloned();
        info!("Content type: {:?}", content_type);

        // Make sure the temp directory exists
        if !Path::new(&self.temp_dir).exists() {
            info!("Creating temp directory: {}", self.temp_dir);
            fs::create_dir_all(&self.temp_dir)
                .map_err(|e| {
                    error!("Failed to create temp directory: {}", e);
                    AppError::InternalError(format!("Failed to create temp directory: {}", e))
                })?;
        }

        // Create file
        info!("Creating temp file...");
        let mut file = match fs::File::create(&filepath) {
            Ok(f) => f,
            Err(e) => {
                error!("Failed to create temp file: {}", e);
                return Err(AppError::InternalError(format!("Failed to create temp file: {}", e)));
            }
        };

        // Write field data to file
        info!("Writing field data to file...");
        let mut total_bytes = 0;
        while let Some(chunk) = field.next().await {
            let data = chunk.map_err(|e| {
                error!("Failed to read upload data: {}", e);
                AppError::ValidationError(format!("Failed to read upload data: {}", e))
            })?;

            file.write_all(&data)
                .map_err(|e| {
                    error!("Failed to write to temp file: {}", e);
                    AppError::InternalError(format!("Failed to write to temp file: {}", e))
                })?;

            total_bytes += data.len();
        }

        info!("Stored uploaded file at: {:?} ({} bytes)", filepath, total_bytes);

        Ok((filepath, content_type))
    }

    // Store a file from bytes
    pub async fn store_bytes(&self, data: &[u8], field_name: &str, content_type: Option<&Mime>) -> Result<(PathBuf, Option<Mime>), AppError> {
        // Generate a unique filename
        let filename = format!("{}.tmp", Uuid::new_v4());
        let filepath = Path::new(&self.temp_dir).join(&filename);

        info!("Storing {} bytes for field '{}' to temp file: {:?}", data.len(), field_name, filepath);
        info!("Content type: {:?}", content_type);

        // Make sure the temp directory exists
        if !Path::new(&self.temp_dir).exists() {
            info!("Creating temp directory: {}", self.temp_dir);
            fs::create_dir_all(&self.temp_dir)
                .map_err(|e| {
                    error!("Failed to create temp directory: {}", e);
                    AppError::InternalError(format!("Failed to create temp directory: {}", e))
                })?;
        }

        // Create file
        info!("Creating temp file...");
        let mut file = match fs::File::create(&filepath) {
            Ok(f) => f,
            Err(e) => {
                error!("Failed to create temp file: {}", e);
                return Err(AppError::InternalError(format!("Failed to create temp file: {}", e)));
            }
        };

        // Write data to file
        info!("Writing data to file...");
        file.write_all(data)
            .map_err(|e| {
                error!("Failed to write to temp file: {}", e);
                AppError::InternalError(format!("Failed to write to temp file: {}", e))
            })?;

        info!("Successfully stored {} bytes for field '{}' at: {:?}", data.len(), field_name, filepath);

        Ok((filepath, content_type.cloned()))
    }

    // Read a file into bytes
    pub fn read_file(&self, filepath: &Path) -> Result<Vec<u8>, AppError> {
        info!("Reading file from: {:?}", filepath);

        if !filepath.exists() {
            error!("File does not exist: {:?}", filepath);
            return Err(AppError::InternalError(format!("File does not exist: {:?}", filepath)));
        }

        match fs::read(filepath) {
            Ok(data) => {
                info!("Successfully read {} bytes from file", data.len());
                Ok(data)
            },
            Err(e) => {
                error!("Failed to read temp file: {}", e);
                Err(AppError::InternalError(format!("Failed to read temp file: {}", e)))
            }
        }
    }

    // Delete a file
    pub fn delete_file(&self, filepath: &Path) -> Result<(), AppError> {
        info!("Attempting to delete file: {:?}", filepath);

        if filepath.exists() {
            match fs::remove_file(filepath) {
                Ok(_) => {
                    info!("Successfully deleted temp file: {:?}", filepath);
                    Ok(())
                },
                Err(e) => {
                    error!("Failed to delete temp file: {}", e);
                    Err(AppError::InternalError(format!("Failed to delete temp file: {}", e)))
                }
            }
        } else {
            info!("File does not exist, nothing to delete: {:?}", filepath);
            Ok(())
        }
    }

    // Clean up old files (older than 1 hour)
    pub fn cleanup_old_files(&self) -> Result<(), AppError> {
        let dir = Path::new(&self.temp_dir);
        if !dir.exists() {
            return Ok(());
        }

        let now = std::time::SystemTime::now();
        let entries = fs::read_dir(dir)
            .map_err(|e| AppError::InternalError(format!("Failed to read temp directory: {}", e)))?;

        for entry in entries {
            if let Ok(entry) = entry {
                let path = entry.path();
                if let Ok(metadata) = fs::metadata(&path) {
                    if let Ok(created) = metadata.created() {
                        if let Ok(duration) = now.duration_since(created) {
                            // Delete files older than 1 hour
                            if duration.as_secs() > 3600 {
                                if let Err(e) = fs::remove_file(&path) {
                                    error!("Failed to delete old temp file {:?}: {}", path, e);
                                } else {
                                    info!("Deleted old temp file: {:?}", path);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }
}

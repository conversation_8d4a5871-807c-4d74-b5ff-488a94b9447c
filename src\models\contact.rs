use chrono::{DateTime, Utc};
use mongodb::bson::oid::ObjectId;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Contact {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: String,
    pub display_name: String,
    pub phone_number: String,
    pub normalized_phone: String, // E.164 format for consistency
    pub is_registered: bool, // Whether this contact is a QuiickChat user
    pub registered_user_id: Option<String>, // If registered, their user ID
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ContactInput {
    pub display_name: String,
    pub phone_number: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BulkContactsRequest {
    pub contacts: Vec<ContactInput>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ContactResponse {
    pub id: String,
    pub display_name: String,
    pub phone_number: String,
    pub is_registered: bool,
    pub registered_user_id: Option<String>,
    pub profile_picture: Option<String>, // From registered user if available
    pub status: Option<String>, // Current status if registered
    pub last_seen_millis: Option<i64>, // Last seen if registered
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BulkContactsResponse {
    pub success: bool,
    pub message: String,
    pub contacts_processed: usize,
    pub contacts_registered: usize,
    pub contacts: Vec<ContactResponse>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ContactSyncResponse {
    pub success: bool,
    pub contacts: Vec<ContactResponse>,
    pub total_contacts: usize,
    pub registered_contacts: usize,
}

impl From<Contact> for ContactResponse {
    fn from(contact: Contact) -> Self {
        ContactResponse {
            id: contact.id.map_or_else(|| "".to_string(), |id| id.to_hex()),
            display_name: contact.display_name,
            phone_number: contact.phone_number,
            is_registered: contact.is_registered,
            registered_user_id: contact.registered_user_id,
            profile_picture: None, // Will be populated by service
            status: None, // Will be populated by service
            last_seen_millis: None, // Will be populated by service
        }
    }
}

// WebSocket message types for real-time status updates
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct StatusUpdateMessage {
    pub message_type: String, // "status_update", "status_deleted", "user_online", etc.
    pub user_id: String,
    pub status_id: Option<String>,
    pub content_type: Option<String>,
    pub content_url: Option<String>,
    pub caption: Option<String>,
    pub created_at_millis: Option<i64>,
    pub expires_at_millis: Option<i64>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebSocketMessage {
    pub message_type: String,
    pub data: serde_json::Value,
    pub timestamp: i64,
}

// WebSocket connection tracking
#[derive(Debug, Clone)]
pub struct WebSocketConnection {
    pub user_id: String,
    pub connection_id: String,
    pub connected_at: DateTime<Utc>,
    pub last_ping: DateTime<Utc>,
}

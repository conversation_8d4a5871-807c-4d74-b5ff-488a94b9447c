use chrono::{Duration, Utc};
use mongodb::{
    bson::{doc, oid::ObjectId, Document, Bson},
    options::FindOneAndUpdateOptions,
    Collection,
};
use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::Mutex;
use log::{info, warn, error};
use jsonwebtoken::{encode, decode, Header, Algorithm, Validation, EncodingKey, Decoding<PERSON>ey};
use serde::{Deserialize, Serialize};

use crate::errors::AppError;
use crate::models::{User, Verification, SafeUserResponse};
use crate::services::mongodb::MongoDBService;
use crate::utils::{generate_username, generate_secure_password, generate_verification_code};

// JWT Claims structure
#[derive(Debug, Serialize, Deserialize)]
pub struct TokenClaims {
    pub sub: String,    // Subject (user ID)
    pub phone: String,  // Phone number
    pub exp: usize,     // Expiration time
    pub iat: usize,     // Issued at
    pub token_type: String, // "access" or "refresh"
}

// Cached token structure
#[derive(Debug, <PERSON>lone)]
pub struct CachedToken {
    pub token: String,
    pub expires_at: chrono::DateTime<Utc>,
    pub token_type: String,
}

impl CachedToken {
    pub fn new(token: String, expires_in_seconds: u32, token_type: String) -> Self {
        let expires_at = Utc::now() + Duration::seconds(expires_in_seconds as i64);
        Self { token, expires_at, token_type }
    }

    pub fn is_valid(&self) -> bool {
        // Consider token valid if it has more than 5 minutes left
        self.expires_at > Utc::now() + Duration::minutes(5)
    }

    pub fn is_expiring_soon(&self) -> bool {
        // Token is expiring soon if it has less than 1 hour left but is still valid
        let now = Utc::now();
        self.expires_at > now && self.expires_at <= now + Duration::hours(1)
    }
}

// Token cache for managing JWT tokens
#[derive(Debug, Clone)]
pub struct TokenCache {
    access_tokens: Arc<Mutex<HashMap<String, CachedToken>>>,
    refresh_tokens: Arc<Mutex<HashMap<String, CachedToken>>>,
}

impl TokenCache {
    pub fn new() -> Self {
        Self {
            access_tokens: Arc::new(Mutex::new(HashMap::new())),
            refresh_tokens: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn get_access_token(&self, user_id: &str) -> Option<String> {
        let tokens = self.access_tokens.lock().await;
        tokens.get(user_id).filter(|t| t.is_valid()).map(|t| t.token.clone())
    }

    pub async fn get_refresh_token(&self, user_id: &str) -> Option<String> {
        let tokens = self.refresh_tokens.lock().await;
        tokens.get(user_id).filter(|t| t.is_valid()).map(|t| t.token.clone())
    }

    pub async fn store_access_token(&self, user_id: String, token: String, expires_in_seconds: u32) {
        let mut tokens = self.access_tokens.lock().await;
        tokens.insert(user_id, CachedToken::new(token, expires_in_seconds, "access".to_string()));
    }

    pub async fn store_refresh_token(&self, user_id: String, token: String, expires_in_seconds: u32) {
        let mut tokens = self.refresh_tokens.lock().await;
        tokens.insert(user_id, CachedToken::new(token, expires_in_seconds, "refresh".to_string()));
    }

    pub async fn remove_tokens(&self, user_id: &str) {
        let mut access_tokens = self.access_tokens.lock().await;
        let mut refresh_tokens = self.refresh_tokens.lock().await;
        access_tokens.remove(user_id);
        refresh_tokens.remove(user_id);
    }


}

pub struct UserService {
    mongodb: Arc<MongoDBService>,
    users_collection: Collection<User>,
    verifications_collection: Collection<Verification>,
    token_cache: TokenCache,
}

impl UserService {
    pub fn new(mongodb: Arc<MongoDBService>) -> Self {
        let users_collection = mongodb.collection::<User>("users");
        let verifications_collection = mongodb.collection::<Verification>("verifications");

        Self {
            mongodb,
            users_collection,
            verifications_collection,
            token_cache: TokenCache::new(),
        }
    }

    // Generate a verification code using the utility function
    pub fn generate_verification_code(&self) -> String {
        generate_verification_code()
    }

    // Create a verification record
    pub async fn create_verification(&self, phone: &str) -> Result<String, AppError> {
        let code = self.generate_verification_code();
        let expires_at = Utc::now() + Duration::minutes(10);
        let now = Utc::now();

        let now_millis = now.timestamp_millis();
        let expires_millis = expires_at.timestamp_millis();

        let verification = Verification {
            id: None,
            phone: phone.to_string(),
            code: code.clone(),
            expires_at_millis: expires_millis,
            created_at_millis: now_millis,
            attempts: 0,
            verified: false,
        };

        let filter = doc! { "phone": phone };
        let existing = self.verifications_collection.find_one(filter.clone(), None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to check for existing verification: {}", e)))?;

        if let Some(_existing) = existing {
            let now_millis = now.timestamp_millis();
            let expires_millis = expires_at.timestamp_millis();

            let update = doc! {
                "$set": {
                    "code": code.clone(),
                    "expires_at_millis": expires_millis,
                    "created_at_millis": now_millis,
                    "attempts": 0,
                    "verified": false
                }
            };

            self.verifications_collection.update_one(filter, update, None).await
                .map_err(|e| AppError::DatabaseError(format!("Failed to update verification record: {}", e)))?;
        } else {

            self.verifications_collection.insert_one(verification, None).await
                .map_err(|e| AppError::DatabaseError(format!("Failed to create verification record: {}", e)))?;
        }

        Ok(code)
    }

    // Verify a code
    pub async fn verify_code(&self, phone: &str, code: &str) -> Result<bool, AppError> {
        let filter = doc! { "phone": phone };
        let verification = self.verifications_collection.find_one(filter.clone(), None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to find verification record: {}", e)))?;

        match verification {
            Some(mut verification) => {
                let now_millis = Utc::now().timestamp_millis();
                if verification.expires_at_millis < now_millis {
                    return Err(AppError::ValidationError("Verification code has expired".to_string()));
                }

                if verification.code != code {
                    verification.attempts += 1;

                    let update = doc! {
                        "$set": { "attempts": verification.attempts }
                    };

                    self.verifications_collection.update_one(filter, update, None).await
                        .map_err(|e| AppError::DatabaseError(format!("Failed to update verification attempts: {}", e)))?;

                    if verification.attempts >= 3 {
                        return Err(AppError::ValidationError("Too many failed attempts. Please request a new code.".to_string()));
                    }

                    return Err(AppError::ValidationError("Invalid verification code".to_string()));
                }

                let update = doc! {
                    "$set": { "verified": true }
                };

                self.verifications_collection.update_one(filter, update, None).await
                    .map_err(|e| AppError::DatabaseError(format!("Failed to mark verification as verified: {}", e)))?;

                Ok(true)
            },
            None => Err(AppError::ValidationError("No verification record found for this phone number".to_string())),
        }
    }

    pub async fn create_user(&self, phone: &str) -> Result<User, AppError> {
        let filter = doc! { "phone": phone };
        let existing_user = self.users_collection.find_one(filter.clone(), None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to check for existing user: {}", e)))?;

        if let Some(existing_user) = existing_user {
            let now = Utc::now();

            let mut update_doc = doc! {
                "$set": {
                    "is_verified": true,
                    "last_seen_millis": now.timestamp_millis(),
                    "updated_at_millis": now.timestamp_millis()
                }
            };

            if existing_user.email.is_none() {
                let email = format!("{}@q.quiickchat.net", phone);
                update_doc = doc! {
                    "$set": {
                        "is_verified": true,
                        "last_seen_millis": now.timestamp_millis(),
                        "updated_at_millis": now.timestamp_millis(),
                        "email": email
                    }
                };
            }

            let options = FindOneAndUpdateOptions::builder()
                .return_document(mongodb::options::ReturnDocument::After)
                .build();

            let updated_user = self.users_collection.find_one_and_update(filter, update_doc, options).await
                .map_err(|e| AppError::DatabaseError(format!("Failed to update user: {}", e)))?;

            match updated_user {
                Some(user) => Ok(user),
                None => Err(AppError::DatabaseError("Failed to update user".to_string())),
            }
        } else {
            let username = generate_username();

            let now = Utc::now();

            let email = format!("{}@q.quiickchat.net", phone);

            let user = User {
                id: None,
                email: Some(email),
                username: Some(username.clone()),
                phone: phone.to_string(),
                profile_picture: None,
                is_verified: true,
                last_seen_millis: now.timestamp_millis(),
                contacts: Vec::new(),
                blocked_users: Vec::new(),
                status: None,
                created_at_millis: now.timestamp_millis(),
                updated_at_millis: now.timestamp_millis(),
                calls: Vec::new(),
                videos: Vec::new(),
                chats: Vec::new(),
                tokens: Vec::new(),
                bio: None,
                address: None,
                identity_key: None,
                signed_pre_key: None,
                pre_key: None,
                one_time_keys: Vec::new(),
                last_key_update: None,
                device_id: None,
                registration_id: None,

            };

            let result = self.users_collection.insert_one(user.clone(), None).await
                .map_err(|e| AppError::DatabaseError(format!("Failed to create user: {}", e)))?;

            let id = result.inserted_id.as_object_id()
                .ok_or_else(|| AppError::DatabaseError("Failed to get inserted user ID".to_string()))?;

            let filter = doc! { "_id": id };
            let created_user = self.users_collection.find_one(filter, None).await
                .map_err(|e| AppError::DatabaseError(format!("Failed to get created user: {}", e)))?;

            match created_user {
                Some(user) => Ok(user),
                None => Err(AppError::DatabaseError("Failed to get created user".to_string())),
            }
        }
    }

    // Get user by ID
    pub async fn get_user_by_id(&self, id: &str) -> Result<User, AppError> {
        let object_id = ObjectId::parse_str(id)
            .map_err(|_| AppError::ValidationError(format!("Invalid user ID: {}", id)))?;

        let filter = doc! { "_id": object_id };
        let user = self.users_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get user: {}", e)))?;

        match user {
            Some(user) => Ok(user),
            None => Err(AppError::NotFoundError(format!("User not found with ID: {}", id))),
        }
    }

    // Get user by phone
    pub async fn get_user_by_phone(&self, phone: &str) -> Result<User, AppError> {
        let clean_phone = phone.trim();
        log::info!("=== PHONE LOOKUP DEBUG ===");
        log::info!("Original phone: '{}'", phone);
        log::info!("Clean phone: '{}'", clean_phone);
        log::info!("Phone length: {}", clean_phone.len());
        log::info!("Phone bytes: {:?}", clean_phone.as_bytes());

        // Try exact match first
        log::info!("Attempt 1: Searching for exact match: '{}'", clean_phone);
        let filter = doc! { "phone": clean_phone };
        let user = self.users_collection.find_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get user: {}", e)))?;

        if let Some(user) = user {
            log::info!("SUCCESS: Found user with exact match");
            return Ok(user);
        }
        log::info!("No match found for exact phone");

        // Try with + prefix if not present
        if !clean_phone.starts_with('+') {
            let phone_with_plus = format!("+{}", clean_phone);
            log::info!("Attempt 2: Trying with + prefix: '{}'", phone_with_plus);

            let filter = doc! { "phone": phone_with_plus };
            let user = self.users_collection.find_one(filter, None).await
                .map_err(|e| AppError::DatabaseError(format!("Failed to get user: {}", e)))?;

            if let Some(user) = user {
                log::info!("SUCCESS: Found user with + prefix");
                return Ok(user);
            }
            log::info!("No match found with + prefix");
        }

        // Try without + prefix if present
        if clean_phone.starts_with('+') {
            let phone_without_plus = &clean_phone[1..];
            log::info!("Attempt 3: Trying without + prefix: '{}'", phone_without_plus);

            let filter = doc! { "phone": phone_without_plus };
            let user = self.users_collection.find_one(filter, None).await
                .map_err(|e| AppError::DatabaseError(format!("Failed to get user: {}", e)))?;

            if let Some(user) = user {
                log::info!("SUCCESS: Found user without + prefix");
                return Ok(user);
            }
            log::info!("No match found without + prefix");
        }

        // Let's also try to list some users to see what's in the database
        log::info!("=== DATABASE DEBUG ===");
        let cursor = self.users_collection.find(doc! {}, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to query users: {}", e)))?;

        use futures::stream::StreamExt;
        let users: Vec<User> = cursor.collect::<Vec<_>>().await
            .into_iter()
            .filter_map(|result| result.ok())
            .take(5) // Just get first 5 users
            .collect();

        log::info!("Found {} users in database", users.len());
        for (i, user) in users.iter().enumerate() {
            log::info!("User {}: phone='{}', verified={}", i+1, user.phone, user.is_verified);
        }
        log::info!("=== END DATABASE DEBUG ===");

        Err(AppError::NotFoundError(format!("User not found with phone: {}", clean_phone)))
    }

    pub async fn update_user_profile(&self, user_id: &str, username: Option<String>, profile_picture: Option<String>, status: Option<String>, bio: Option<String>, address: Option<String>) -> Result<User, AppError> {
        let object_id = ObjectId::parse_str(user_id)
            .map_err(|_| AppError::ValidationError(format!("Invalid user ID: {}", user_id)))?;

        let filter = doc! { "_id": object_id };
        let current_user = self.users_collection.find_one(filter.clone(), None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get user: {}", e)))?;

        let current_user = match current_user {
            Some(user) => user,
            None => return Err(AppError::NotFoundError(format!("User not found with ID: {}", user_id))),
        };

        let mut update_doc = Document::new();
        let mut set_doc = Document::new();

        if let Some(username) = username {
            set_doc.insert("username", username.clone());
        }

        if let Some(profile_picture) = profile_picture {
            set_doc.insert("profile_picture", profile_picture.clone());
        }

        if let Some(status) = status {
            set_doc.insert("status", status);
        }

        if let Some(bio) = bio {
            set_doc.insert("bio", bio);
        }

        if let Some(address) = address {
            set_doc.insert("address", address);
        }

        let now = Utc::now();
        let _now_bson = Bson::DateTime(mongodb::bson::DateTime::from_millis(now.timestamp_millis()));
        set_doc.insert("updated_at_millis", now.timestamp_millis());

        update_doc.insert("$set", set_doc);

        let options = FindOneAndUpdateOptions::builder()
            .return_document(mongodb::options::ReturnDocument::After)
            .build();

        let updated_user = self.users_collection.find_one_and_update(filter, update_doc, options).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to update user profile: {}", e)))?;

        match updated_user {
            Some(user) => Ok(user),
            None => Err(AppError::NotFoundError(format!("User not found with ID: {}", user_id))),
        }
    }

    pub async fn update_last_seen(&self, user_id: &str) -> Result<(), AppError> {
        let object_id = ObjectId::parse_str(user_id)
            .map_err(|_| AppError::ValidationError(format!("Invalid user ID: {}", user_id)))?;

        let filter = doc! { "_id": object_id };
        let now = Utc::now();
        let _now_bson = Bson::DateTime(mongodb::bson::DateTime::from_millis(now.timestamp_millis()));

        let update = doc! {
            "$set": {
                "last_seen_millis": now.timestamp_millis(),
                "updated_at_millis": now.timestamp_millis()
            }
        };

        self.users_collection.update_one(filter, update, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to update last seen: {}", e)))?;

        Ok(())
    }

    pub async fn delete_user(&self, user_id: &str) -> Result<(), AppError> {
        let object_id = ObjectId::parse_str(user_id)
            .map_err(|_| AppError::ValidationError(format!("Invalid user ID: {}", user_id)))?;

        let filter = doc! { "_id": object_id };

        let result = self.users_collection.delete_one(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to delete user: {}", e)))?;

        if result.deleted_count == 0 {
            return Err(AppError::NotFoundError(format!("User not found with ID: {}", user_id)));
        }

        Ok(())
    }

    pub async fn delete_verification_records(&self, phone: &str) -> Result<(), AppError> {
        let filter = doc! { "phone": phone };

        self.verifications_collection.delete_many(filter, None).await
            .map_err(|e| AppError::DatabaseError(format!("Failed to delete verification records: {}", e)))?;

        Ok(())
    }

    pub fn to_safe_user_response(&self, user: User) -> SafeUserResponse {
        SafeUserResponse::from(user)
    }

    // JWT Token Management Methods

    /// Generate JWT access token (15 minutes expiry)
    pub async fn generate_access_token(&self, user: &User) -> Result<String, AppError> {
        let user_id = user.id.as_ref()
            .ok_or_else(|| AppError::ValidationError("User ID is required".to_string()))?
            .to_hex();

        let now = Utc::now();
        let exp = (now + Duration::minutes(15)).timestamp() as usize;
        let iat = now.timestamp() as usize;

        let claims = TokenClaims {
            sub: user_id.clone(),
            phone: user.phone.clone(),
            exp,
            iat,
            token_type: "access".to_string(),
        };

        let jwt_secret = std::env::var("JWT_SECRET")
            .unwrap_or_else(|_| "default_secret_change_in_production".to_string());

        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(jwt_secret.as_ref()),
        ).map_err(|e| AppError::InternalError(format!("Failed to generate access token: {}", e)))?;

        // Cache the token
        self.token_cache.store_access_token(user_id, token.clone(), 900).await; // 15 minutes

        Ok(token)
    }

    /// Generate JWT refresh token (7 days expiry)
    pub async fn generate_refresh_token(&self, user: &User) -> Result<String, AppError> {
        let user_id = user.id.as_ref()
            .ok_or_else(|| AppError::ValidationError("User ID is required".to_string()))?
            .to_hex();

        let now = Utc::now();
        let exp = (now + Duration::days(7)).timestamp() as usize;
        let iat = now.timestamp() as usize;

        let claims = TokenClaims {
            sub: user_id.clone(),
            phone: user.phone.clone(),
            exp,
            iat,
            token_type: "refresh".to_string(),
        };

        let jwt_secret = std::env::var("JWT_SECRET")
            .unwrap_or_else(|_| "default_secret_change_in_production".to_string());

        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(jwt_secret.as_ref()),
        ).map_err(|e| AppError::InternalError(format!("Failed to generate refresh token: {}", e)))?;

        // Cache the token
        self.token_cache.store_refresh_token(user_id, token.clone(), 604800).await; // 7 days

        Ok(token)
    }

    /// Generate both access and refresh tokens
    pub async fn generate_token_pair(&self, user: &User) -> Result<(String, String), AppError> {
        let access_token = self.generate_access_token(user).await?;
        let refresh_token = self.generate_refresh_token(user).await?;
        Ok((access_token, refresh_token))
    }

    /// Validate and decode JWT token
    pub async fn validate_token(&self, token: &str) -> Result<TokenClaims, AppError> {
        let jwt_secret = std::env::var("JWT_SECRET")
            .unwrap_or_else(|_| "default_secret_change_in_production".to_string());

        let validation = Validation::new(Algorithm::HS256);

        let token_data = decode::<TokenClaims>(
            token,
            &DecodingKey::from_secret(jwt_secret.as_ref()),
            &validation,
        ).map_err(|e| AppError::AuthenticationError(format!("Invalid token: {}", e)))?;

        Ok(token_data.claims)
    }

    /// Refresh access token using refresh token
    pub async fn refresh_access_token(&self, refresh_token: &str) -> Result<String, AppError> {
        // Validate the refresh token
        let claims = self.validate_token(refresh_token).await?;

        if claims.token_type != "refresh" {
            return Err(AppError::AuthenticationError("Invalid token type".to_string()));
        }

        // Get the user
        let user = self.get_user_by_id(&claims.sub).await?;

        // Generate new access token
        self.generate_access_token(&user).await
    }

    /// Get cached access token for user
    pub async fn get_cached_access_token(&self, user_id: &str) -> Option<String> {
        self.token_cache.get_access_token(user_id).await
    }

    /// Get cached refresh token for user
    pub async fn get_cached_refresh_token(&self, user_id: &str) -> Option<String> {
        self.token_cache.get_refresh_token(user_id).await
    }

    /// Logout user (remove cached tokens)
    pub async fn logout_user(&self, user_id: &str) -> Result<(), AppError> {
        self.token_cache.remove_tokens(user_id).await;
        info!("User {} logged out successfully", user_id);
        Ok(())
    }



    /// Validate user session and return user info
    pub async fn validate_session(&self, token: &str) -> Result<User, AppError> {
        let claims = self.validate_token(token).await?;

        if claims.token_type != "access" {
            return Err(AppError::AuthenticationError("Invalid token type for session".to_string()));
        }

        self.get_user_by_id(&claims.sub).await
    }

    // Method to notify contact service about user registration
    // This should be called after successful user verification
    pub async fn notify_contact_registration(&self, phone: &str, user_id: &str) -> Result<(), AppError> {
        // This is a placeholder - in a real implementation, you would inject ContactService
        // For now, we'll just log the event
        log::info!("User registered: phone={}, user_id={}", phone, user_id);
        Ok(())
    }
}

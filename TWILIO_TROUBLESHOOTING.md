# Twilio Authentication Error Troubleshooting

## 🚨 **Current Issue**
You're getting this error:
```json
{
    "success": false,
    "message": "External service error: Twilio API error: {\"code\":20003,\"message\":\"Authenticate\",\"more_info\":\"https://www.twilio.com/docs/errors/20003\",\"status\":401}"
}
```

**Error Code 20003** means "Authentication Failed" - your Twilio credentials are invalid or expired.

## 🔍 **Diagnostic Steps**

### 1. **Test Twilio Credentials**
First, test if your credentials are valid:

```bash
# Start the server
cargo run

# Test Twilio credentials
curl -X GET "http://localhost:8080/api/v1/users/debug-twilio"
```

### 2. **Check Current Credentials**
Your current `.env` file has:
```
TWILIO_ACCOUNT_SID="**********************************"
TWILIO_AUTH_TOKEN="9c9643ca657ef3d74c4589aec7a550a1"
TWILIO_PHONE_NUMBER="+***********"
```

### 3. **Verify Credentials in Twilio Console**
1. Go to [Twilio Console](https://console.twilio.com/)
2. Navigate to **Account > API keys & tokens**
3. Check if your Account SID and Auth Token are correct
4. Verify your phone number is active and verified

## 🛠️ **Common Solutions**

### **Solution 1: Update Credentials**
Your credentials might be expired or incorrect. Get fresh ones:

1. **Login to Twilio Console**: https://console.twilio.com/
2. **Get Account SID**: Found on the main dashboard
3. **Get Auth Token**: Click "View" next to Auth Token on dashboard
4. **Verify Phone Number**: Go to Phone Numbers > Manage > Active numbers

Update your `.env` file:
```bash
TWILIO_ACCOUNT_SID="YOUR_NEW_ACCOUNT_SID"
TWILIO_AUTH_TOKEN="YOUR_NEW_AUTH_TOKEN"
TWILIO_PHONE_NUMBER="YOUR_VERIFIED_PHONE_NUMBER"
```

### **Solution 2: Check Account Status**
- Ensure your Twilio account is active (not suspended)
- Check if you have sufficient balance for SMS
- Verify your account is not in trial mode restrictions

### **Solution 3: Phone Number Format**
Ensure your Twilio phone number is in E.164 format:
```
✅ Correct: +***********
❌ Wrong: **********
❌ Wrong: (*************
```

### **Solution 4: Test with Curl**
Test Twilio API directly:

```bash
curl -X POST https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json \
  --data-urlencode "To=+**********" \
  --data-urlencode "From=+***********" \
  --data-urlencode "Body=Test message" \
  -u **********************************:9c9643ca657ef3d74c4589aec7a550a1
```

Replace with your actual credentials and test phone number.

## 🔧 **Debug Mode**

I've added debug logging to help diagnose the issue. When you call the login API, check the server logs for:

```
=== TWILIO DEBUG ===
SMS enabled: true
Account SID: ACb84f...71d
Auth Token: SET (hidden)
From Number: +***********
To Number: +**************
Message to send: Your QuiickChat verification code is: 123456
Twilio API URL: https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json
Request payload: To=+**************, From=+***********, Body=Your QuiickChat verification code is: 123456
Twilio response status: 401
Twilio error response: {"code":20003,"message":"Authenticate","more_info":"https://www.twilio.com/docs/errors/20003","status":401}
```

## 🚀 **Quick Fix for Testing**

If you want to test the login flow without SMS, you can temporarily disable SMS:

1. **Add to `.env`**:
```bash
SMS_ENABLED=false
```

2. **Restart the server**

3. **Test login** - it will skip SMS and just log the code to console

## 📱 **Alternative: Use Twilio Trial Account**

If you're using a trial account:

1. **Verify your phone number** in Twilio Console
2. **Add the destination phone number** to verified numbers
3. **Check trial limitations** - trial accounts can only send to verified numbers

## 🔍 **Debug Endpoints**

I've added these debug endpoints to help:

1. **Test Phone Lookup**:
```bash
curl -X GET "http://localhost:8080/api/v1/users/debug-phone?phone=%2B**************"
```

2. **Test Twilio Credentials**:
```bash
curl -X GET "http://localhost:8080/api/v1/users/debug-twilio"
```

## 📋 **Next Steps**

1. **Run the debug endpoints** to see what's failing
2. **Check Twilio Console** for account status and correct credentials
3. **Update `.env` file** with fresh credentials
4. **Test with a verified phone number** if using trial account
5. **Check server logs** for detailed error information

## 💡 **Pro Tips**

- **Trial accounts** can only send to verified phone numbers
- **International numbers** might need country-specific Twilio numbers
- **Rate limits** might apply - check Twilio usage dashboard
- **Account suspension** can happen due to policy violations

Let me know what the debug endpoints return, and we can solve this quickly! 🚀

import { Request, Response } from 'express';
import config from '@/config';
import { HealthCheckResponse, ApiResponse } from '@/types';
import logger from '@/utils/logger';

export class HealthController {
  /**
   * Health check endpoint - no authentication required
   */
  static async getHealth(req: Request, res: Response): Promise<void> {
    try {
      const uptime = process.uptime();
      
      const healthData: HealthCheckResponse = {
        success: true,
        service: 'QuiickChat EC2 Manager API',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        region: config.aws.region,
        uptime: Math.floor(uptime),
        environment: config.nodeEnv,
      };

      const response: ApiResponse<HealthCheckResponse> = {
        success: true,
        data: healthData,
        message: 'Service is healthy',
        timestamp: new Date().toISOString(),
      };

      logger.debug('Health check requested', { 
        ip: req.ip, 
        userAgent: req.get('User-Agent') 
      });

      res.status(200).json(response);
    } catch (error) {
      logger.error('Health check failed', { error });
      
      const response: ApiResponse = {
        success: false,
        message: 'Health check failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        code: 'HEALTH_CHECK_FAILED',
        timestamp: new Date().toISOString(),
      };

      res.status(500).json(response);
    }
  }
}

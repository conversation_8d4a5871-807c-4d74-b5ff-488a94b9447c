use actix_web::{web, HttpRequest, HttpResponse, Result};
use actix_web_actors::ws;
use std::sync::Arc;

use crate::errors::AppError;
use crate::models::{BulkContactsRequest, ApiResponse};
use crate::services::{ContactService, WebSocketService};
use crate::services::websocket_service::WebSocketActor;

// Controller state for dependency injection
pub struct ContactControllerState {
    pub contact_service: Arc<ContactService>,
    pub websocket_service: Arc<WebSocketService>,
}

// Upload contacts from device
pub async fn upload_contacts(
    req: HttpRequest,
    data: web::Data<ContactControllerState>,
    json: web::Json<BulkContactsRequest>,
) -> Result<HttpResponse, AppError> {
    // Extract user ID from JWT token (set by auth middleware)
    let user_id = req.extensions()
        .get::<String>()
        .ok_or_else(|| AppError::AuthenticationError("User not authenticated".to_string()))?
        .clone();

    // Validate input
    if json.contacts.is_empty() {
        return Ok(HttpResponse::BadRequest().json(ApiResponse {
            success: false,
            message: "No contacts provided".to_string(),
            data: None,
        }));
    }

    if json.contacts.len() > 5000 {
        return Ok(HttpResponse::BadRequest().json(ApiResponse {
            success: false,
            message: "Too many contacts. Maximum 5000 contacts allowed per upload".to_string(),
            data: None,
        }));
    }

    // Validate phone number formats
    for contact in &json.contacts {
        if contact.display_name.trim().is_empty() {
            return Ok(HttpResponse::BadRequest().json(ApiResponse {
                success: false,
                message: "Contact display name cannot be empty".to_string(),
                data: None,
            }));
        }

        if !contact.phone_number.starts_with('+') {
            return Ok(HttpResponse::BadRequest().json(ApiResponse {
                success: false,
                message: format!("Phone number must include country code (e.g., +1, +234, +44): {}", contact.phone_number),
                data: None,
            }));
        }
    }

    // Upload contacts
    match data.contact_service.upload_contacts(&user_id, json.contacts.clone()).await {
        Ok(response) => {
            log::info!("Contacts uploaded for user {}: {} contacts processed, {} registered", 
                      user_id, response.contacts_processed, response.contacts_registered);
            
            Ok(HttpResponse::Ok().json(ApiResponse {
                success: true,
                message: response.message,
                data: Some(serde_json::to_value(response)?),
            }))
        }
        Err(e) => {
            log::error!("Failed to upload contacts for user {}: {}", user_id, e);
            Err(e)
        }
    }
}

// Get user's contacts
pub async fn get_contacts(
    req: HttpRequest,
    data: web::Data<ContactControllerState>,
) -> Result<HttpResponse, AppError> {
    // Extract user ID from JWT token
    let user_id = req.extensions()
        .get::<String>()
        .ok_or_else(|| AppError::AuthenticationError("User not authenticated".to_string()))?
        .clone();

    match data.contact_service.get_user_contacts(&user_id).await {
        Ok(response) => {
            Ok(HttpResponse::Ok().json(ApiResponse {
                success: true,
                message: "Contacts retrieved successfully".to_string(),
                data: Some(serde_json::to_value(response)?),
            }))
        }
        Err(e) => {
            log::error!("Failed to get contacts for user {}: {}", user_id, e);
            Err(e)
        }
    }
}

// Sync contacts (re-check registration status)
pub async fn sync_contacts(
    req: HttpRequest,
    data: web::Data<ContactControllerState>,
) -> Result<HttpResponse, AppError> {
    // Extract user ID from JWT token
    let user_id = req.extensions()
        .get::<String>()
        .ok_or_else(|| AppError::AuthenticationError("User not authenticated".to_string()))?
        .clone();

    match data.contact_service.get_user_contacts(&user_id).await {
        Ok(response) => {
            log::info!("Contacts synced for user {}: {} total, {} registered", 
                      user_id, response.total_contacts, response.registered_contacts);
            
            Ok(HttpResponse::Ok().json(ApiResponse {
                success: true,
                message: "Contacts synced successfully".to_string(),
                data: Some(serde_json::to_value(response)?),
            }))
        }
        Err(e) => {
            log::error!("Failed to sync contacts for user {}: {}", user_id, e);
            Err(e)
        }
    }
}

// WebSocket endpoint for real-time status updates
pub async fn websocket_handler(
    req: HttpRequest,
    stream: web::Payload,
    data: web::Data<ContactControllerState>,
) -> Result<HttpResponse, actix_web::Error> {
    // Extract user ID from query parameters or JWT token
    let user_id = if let Some(query_user_id) = req.uri().query()
        .and_then(|q| q.split('&')
            .find(|param| param.starts_with("user_id="))
            .map(|param| param.split('=').nth(1).unwrap_or("").to_string())) {
        query_user_id
    } else {
        // Try to get from JWT token if available
        req.extensions()
            .get::<String>()
            .cloned()
            .unwrap_or_else(|| "anonymous".to_string())
    };

    if user_id == "anonymous" || user_id.is_empty() {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "success": false,
            "message": "User ID required for WebSocket connection"
        })));
    }

    log::info!("WebSocket connection request from user: {}", user_id);

    // Create WebSocket actor
    let websocket_actor = WebSocketActor::new(user_id, data.websocket_service.clone());

    // Start WebSocket connection
    ws::start(websocket_actor, &req, stream)
}

// Get WebSocket connection stats (for debugging)
pub async fn websocket_stats(
    _req: HttpRequest,
    data: web::Data<ContactControllerState>,
) -> Result<HttpResponse, AppError> {
    let total_connections = data.websocket_service.get_total_connections().await;
    
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "success": true,
        "message": "WebSocket statistics",
        "data": {
            "total_connections": total_connections,
            "timestamp": chrono::Utc::now().timestamp_millis()
        }
    })))
}

// Test endpoint to broadcast a message to user's contacts
pub async fn test_broadcast(
    req: HttpRequest,
    data: web::Data<ContactControllerState>,
) -> Result<HttpResponse, AppError> {
    // Extract user ID from JWT token
    let user_id = req.extensions()
        .get::<String>()
        .ok_or_else(|| AppError::AuthenticationError("User not authenticated".to_string()))?
        .clone();

    // Create a test status update message
    let status_message = crate::models::StatusUpdateMessage {
        message_type: "status_update".to_string(),
        user_id: user_id.clone(),
        status_id: Some("test_status_123".to_string()),
        content_type: Some("text".to_string()),
        content_url: None,
        caption: Some("Test status update from API".to_string()),
        created_at_millis: Some(chrono::Utc::now().timestamp_millis()),
        expires_at_millis: Some(chrono::Utc::now().timestamp_millis() + 86400000), // 24 hours
        is_active: Some(true),
    };

    // Broadcast to contacts
    data.websocket_service.broadcast_status_to_contacts(&user_id, status_message).await;

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "success": true,
        "message": "Test broadcast sent to contacts",
        "data": {
            "user_id": user_id,
            "timestamp": chrono::Utc::now().timestamp_millis()
        }
    })))
}

use std::process::Command;
use std::env;
use std::path::Path;

fn main() {
    // Only build the Go library if it doesn't exist
    let out_dir = env::var("OUT_DIR").unwrap();
    let lib_path = Path::new(&out_dir).join("libtoken_lib.a");

    if !lib_path.exists() {
        // Check if Go is installed
        let go_check = Command::new("go")
            .arg("version")
            .output();

        if let Ok(output) = go_check {
            if output.status.success() {
                println!("Go is installed, building token library");

                // Build the Go library
                let status = Command::new("go")
                    .args(&["build", "-buildmode=c-archive", "-o", &format!("{}/libtoken_lib.a", out_dir), "go_token_generator/token_lib.go"])
                    .status();

                if let Ok(status) = status {
                    if !status.success() {
                        println!("Warning: Failed to build Go library, will use Rust fallback");
                    } else {
                        // Link the Go library
                        println!("cargo:rustc-link-search=native={}", out_dir);
                        println!("cargo:rustc-link-lib=static=token_lib");

                        // Link system libraries required by Go
                        if cfg!(target_os = "linux") {
                            println!("cargo:rustc-link-lib=pthread");
                            println!("cargo:rustc-link-lib=dl");
                            println!("cargo:rustc-link-lib=m");
                        } else if cfg!(target_os = "macos") {
                            println!("cargo:rustc-link-lib=framework=CoreFoundation");
                            println!("cargo:rustc-link-lib=framework=Security");
                        }

                        // Define a feature to indicate that the Go library is available
                        println!("cargo:rustc-cfg=feature=\"go_token_lib\"");
                    }
                } else {
                    println!("Warning: Failed to execute Go build command, will use Rust fallback");
                }
            } else {
                println!("Warning: Go is not installed or not in PATH, will use Rust fallback");
            }
        } else {
            println!("Warning: Failed to check Go version, will use Rust fallback");
        }
    } else {
        // Library already exists
        println!("cargo:rustc-link-search=native={}", out_dir);
        println!("cargo:rustc-link-lib=static=token_lib");

        // Link system libraries required by Go
        if cfg!(target_os = "linux") {
            println!("cargo:rustc-link-lib=pthread");
            println!("cargo:rustc-link-lib=dl");
            println!("cargo:rustc-link-lib=m");
        } else if cfg!(target_os = "macos") {
            println!("cargo:rustc-link-lib=framework=CoreFoundation");
            println!("cargo:rustc-link-lib=framework=Security");
        }

        // Define a feature to indicate that the Go library is available
        println!("cargo:rustc-cfg=feature=\"go_token_lib\"");
    }

    // Rebuild if the Go source changes
    println!("cargo:rerun-if-changed=go_token_generator/token_lib.go");
}

use actix_web::{web, HttpResponse};
use serde_json::json;

pub mod users_route;
pub mod keys_route;
pub mod status_route;
pub mod contact_route;

// Simple health check endpoint for monitoring and deployment platforms
async fn health_check() -> HttpResponse {
    HttpResponse::Ok().json(json!({
        "status": "ok",
        "message": "Service is running"
    }))
}

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    // Add health check endpoint at the root level
    cfg.route("/health", web::get().to(health_check));
    // Configure API routes with /api/v1 prefix
    cfg.service(
        web::scope("/api/v1")
            // User routes
            .configure(users_route::configure_routes)

            // Keys routes
            .configure(keys_route::configure_routes)

            // Status routes
            .configure(status_route::configure_routes)

            // Contact routes
            .configure(contact_route::configure_routes)

            // Auth routes that map to the users controller functions
            .service(
                web::scope("/auth")
                    .route("/register", web::post().to(crate::controllers::users_controller::register))
                    .route("/login", web::post().to(crate::controllers::users_controller::login))
                    .route("/verify", web::post().to(crate::controllers::users_controller::verify))
                    .route("/verify-login", web::post().to(crate::controllers::users_controller::verify_login))
                    .route("/refresh-token", web::post().to(crate::controllers::users_controller::refresh_token))
                    .route("/resend-code", web::post().to(crate::controllers::users_controller::resend_verification_code))
                    .route("/init", web::get().to(crate::controllers::users_controller::init_user))
            )
    );
}

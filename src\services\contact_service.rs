use std::sync::Arc;
use chrono::Utc;
use mongodb::{
    bson::{doc, oid::ObjectId, Bson},
    Collection,
    options::{FindOptions, UpdateOptions},
};
use futures::stream::TryStreamExt;
use regex::Regex;
use once_cell::sync::Lazy;

use crate::errors::AppError;
use crate::models::{
    Contact, ContactInput, ContactResponse, BulkContactsResponse, ContactSyncResponse, User
};
use crate::services::MongoDBService;

static PHONE_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"[^\d+]").unwrap()
});

pub struct ContactService {
    mongodb: Arc<MongoDBService>,
    contacts_collection: Collection<Contact>,
    users_collection: Collection<User>,
}

impl ContactService {
    pub fn new(mongodb: Arc<MongoDBService>) -> Self {
        let contacts_collection = mongodb.collection::<Contact>("contacts");
        let users_collection = mongodb.collection::<User>("users");

        Self {
            mongodb,
            contacts_collection,
            users_collection,
        }
    }

    // Normalize phone number to E.164 format
    fn normalize_phone(&self, phone: &str) -> Result<String, AppError> {
        let cleaned = PHONE_REGEX.replace_all(phone, "");

        // Phone number must start with + for international format
        if !cleaned.starts_with('+') {
            return Err(AppError::ValidationError(
                format!("Phone number must include country code (e.g., +1, +234, +44): {}", phone)
            ));
        }

        // Validate minimum length (country code + number)
        if cleaned.len() < 8 {
            return Err(AppError::ValidationError(
                format!("Phone number too short: {}", phone)
            ));
        }

        // Validate maximum length
        if cleaned.len() > 15 {
            return Err(AppError::ValidationError(
                format!("Phone number too long: {}", phone)
            ));
        }

        Ok(cleaned.to_string())
    }

    // Check if a phone number is registered in the system
    async fn check_registration(&self, normalized_phone: &str) -> Result<Option<User>, AppError> {
        let filter = doc! { "phone": normalized_phone };
        
        self.users_collection
            .find_one(filter, None)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to check registration: {}", e)))
    }

    // Upload contacts in bulk
    pub async fn upload_contacts(&self, user_id: &str, contacts: Vec<ContactInput>) -> Result<BulkContactsResponse, AppError> {
        let now = Utc::now();
        let mut processed_contacts = Vec::new();
        let mut registered_count = 0;

        // First, delete existing contacts for this user to avoid duplicates
        let delete_filter = doc! { "user_id": user_id };
        self.contacts_collection
            .delete_many(delete_filter, None)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to delete existing contacts: {}", e)))?;

        for contact_input in contacts {
            let normalized_phone = match self.normalize_phone(&contact_input.phone_number) {
                Ok(phone) => phone,
                Err(e) => {
                    log::warn!("Invalid phone number for contact {}: {}", contact_input.display_name, e);
                    continue; // Skip invalid phone numbers
                }
            };

            // Check if this phone number is registered
            let registered_user = self.check_registration(&normalized_phone).await?;
            let is_registered = registered_user.is_some();
            let registered_user_id = registered_user.as_ref().map(|u| {
                u.id.as_ref().map_or_else(|| "".to_string(), |id| id.to_hex())
            });

            if is_registered {
                registered_count += 1;
            }

            let contact = Contact {
                id: None,
                user_id: user_id.to_string(),
                display_name: contact_input.display_name.clone(),
                phone_number: contact_input.phone_number.clone(),
                normalized_phone: normalized_phone.clone(),
                is_registered,
                registered_user_id: registered_user_id.clone(),
                created_at: now,
                updated_at: now,
            };

            // Insert the contact
            let result = self.contacts_collection
                .insert_one(contact.clone(), None)
                .await
                .map_err(|e| AppError::DatabaseError(format!("Failed to insert contact: {}", e)))?;

            let contact_id = result.inserted_id.as_object_id()
                .ok_or_else(|| AppError::DatabaseError("Failed to get inserted contact ID".to_string()))?;

            let mut contact_response = ContactResponse::from(contact);
            contact_response.id = contact_id.to_hex();

            // Add user details if registered
            if let Some(user) = registered_user {
                contact_response.profile_picture = user.profile_picture;
                contact_response.status = user.status;
                contact_response.last_seen_millis = Some(user.last_seen_millis);
            }

            processed_contacts.push(contact_response);
        }

        Ok(BulkContactsResponse {
            success: true,
            message: format!("Successfully processed {} contacts", processed_contacts.len()),
            contacts_processed: processed_contacts.len(),
            contacts_registered: registered_count,
            contacts: processed_contacts,
        })
    }

    // Get all contacts for a user
    pub async fn get_user_contacts(&self, user_id: &str) -> Result<ContactSyncResponse, AppError> {
        let filter = doc! { "user_id": user_id };
        let options = FindOptions::builder()
            .sort(doc! { "display_name": 1 })
            .build();

        let mut cursor = self.contacts_collection
            .find(filter, options)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get contacts: {}", e)))?;

        let mut contacts = Vec::new();
        let mut registered_count = 0;

        while let Some(contact) = cursor.try_next().await
            .map_err(|e| AppError::DatabaseError(format!("Failed to iterate contacts: {}", e)))? {
            
            let mut contact_response = ContactResponse::from(contact.clone());

            // If contact is registered, get their current user details
            if contact.is_registered {
                registered_count += 1;
                
                if let Some(user_id) = &contact.registered_user_id {
                    if let Ok(Some(user)) = self.get_user_by_id(user_id).await {
                        contact_response.profile_picture = user.profile_picture;
                        contact_response.status = user.status;
                        contact_response.last_seen_millis = Some(user.last_seen_millis);
                    }
                }
            }

            contacts.push(contact_response);
        }

        Ok(ContactSyncResponse {
            success: true,
            total_contacts: contacts.len(),
            registered_contacts: registered_count,
            contacts,
        })
    }

    // Get user by ID helper
    async fn get_user_by_id(&self, user_id: &str) -> Result<Option<User>, AppError> {
        let object_id = ObjectId::parse_str(user_id)
            .map_err(|_| AppError::ValidationError("Invalid user ID format".to_string()))?;
        
        let filter = doc! { "_id": object_id };
        
        self.users_collection
            .find_one(filter, None)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get user: {}", e)))
    }

    // Get contacts who are registered users (for status broadcasting)
    pub async fn get_registered_contacts(&self, user_id: &str) -> Result<Vec<String>, AppError> {
        let filter = doc! { 
            "user_id": user_id,
            "is_registered": true,
            "registered_user_id": { "$ne": null }
        };

        let mut cursor = self.contacts_collection
            .find(filter, None)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get registered contacts: {}", e)))?;

        let mut registered_user_ids = Vec::new();

        while let Some(contact) = cursor.try_next().await
            .map_err(|e| AppError::DatabaseError(format!("Failed to iterate contacts: {}", e)))? {
            
            if let Some(registered_user_id) = contact.registered_user_id {
                registered_user_ids.push(registered_user_id);
            }
        }

        Ok(registered_user_ids)
    }

    // Get users who have this user in their contacts (for reverse lookup)
    pub async fn get_users_with_contact(&self, user_phone: &str) -> Result<Vec<String>, AppError> {
        let normalized_phone = self.normalize_phone(user_phone)?;
        
        let filter = doc! { 
            "normalized_phone": normalized_phone,
            "is_registered": true
        };

        let mut cursor = self.contacts_collection
            .find(filter, None)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get users with contact: {}", e)))?;

        let mut user_ids = Vec::new();

        while let Some(contact) = cursor.try_next().await
            .map_err(|e| AppError::DatabaseError(format!("Failed to iterate contacts: {}", e)))? {
            
            user_ids.push(contact.user_id);
        }

        Ok(user_ids)
    }

    // Update contact registration status when a user registers
    pub async fn update_contact_registration(&self, phone: &str, user_id: &str) -> Result<(), AppError> {
        let normalized_phone = self.normalize_phone(phone)?;
        
        let filter = doc! { "normalized_phone": normalized_phone };
        let update = doc! {
            "$set": {
                "is_registered": true,
                "registered_user_id": user_id,
                "updated_at": Utc::now()
            }
        };

        let options = UpdateOptions::builder().upsert(false).build();
        
        self.contacts_collection
            .update_many(filter, update, options)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to update contact registration: {}", e)))?;

        Ok(())
    }
}

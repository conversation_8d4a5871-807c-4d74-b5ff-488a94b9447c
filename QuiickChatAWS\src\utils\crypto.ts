import crypto from 'crypto';

/**
 * Create a hash of the instance ID for redacted responses
 */
export function hashInstanceId(instanceId: string): string {
  return crypto.createHash('md5').update(instanceId).digest('hex').substring(0, 8);
}

/**
 * Generate a secure random API key
 */
export function generateApiKey(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Redact sensitive information from strings
 */
export function redactString(str: string, visibleChars: number = 4): string {
  if (str.length <= visibleChars * 2) {
    return '*'.repeat(str.length);
  }
  
  const start = str.substring(0, visibleChars);
  const end = str.substring(str.length - visibleChars);
  const middle = '*'.repeat(str.length - (visibleChars * 2));
  
  return `${start}${middle}${end}`;
}

/**
 * Validate API key format
 */
export function isValidApiKey(key: string): boolean {
  return typeof key === 'string' && key.length >= 8 && /^[a-zA-Z0-9-_]+$/.test(key);
}

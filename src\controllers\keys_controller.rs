use actix_web::{web, HttpResponse};
use chrono::Utc;

use std::sync::Arc;

use serde::{Deserialize, Serialize};

use crate::errors::AppError;
use crate::models::ApiResponse;
use crate::services::KeysService;

// Query parameters for user ID
#[derive(Debug, Serialize, Deserialize)]
pub struct UserIdQuery {
    pub user_id: Option<String>,
}

// Shared state for the controller
pub struct KeysControllerState {
    pub keys_service: Arc<KeysService>,
}



// Generate encryption keys for a user
pub async fn generate_encryption_keys(
    data: web::Data<KeysControllerState>,
    query: web::Query<UserIdQuery>,
    body: Option<web::Json<serde_json::Value>>,
) -> Result<HttpResponse, AppError> {
    // Get user ID from query parameters
    let user_id = if let Some(id) = &query.user_id {
        if !id.is_empty() {
            id.clone()
        } else {
            // Try to get from body
            if let Some(body_data) = &body {
                if let Some(id) = body_data.get("user_id").and_then(|v| v.as_str()) {
                    id.to_string()
                } else {
                    return Err(AppError::ValidationError("user_id is required".to_string()));
                }
            } else {
                return Err(AppError::ValidationError("user_id is required".to_string()));
            }
        }
    } else {
        // Try to get from body
        if let Some(body_data) = &body {
            if let Some(id) = body_data.get("user_id").and_then(|v| v.as_str()) {
                id.to_string()
            } else {
                return Err(AppError::ValidationError("user_id is required".to_string()));
            }
        } else {
            return Err(AppError::ValidationError("user_id is required".to_string()));
        }
    };

    // Generate keys
    let keys = data.keys_service.generate_keys(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Encryption keys generated successfully. Store these keys securely as private keys will not be accessible again.".to_string(),
        data: Some(keys),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    // Set cache control headers to prevent caching of sensitive information
    Ok(HttpResponse::Ok()
        .append_header(("Cache-Control", "no-store"))
        .append_header(("Pragma", "no-cache"))
        .json(response))
}

// Rotate encryption keys for a user
pub async fn rotate_encryption_keys(
    data: web::Data<KeysControllerState>,
    query: web::Query<UserIdQuery>,
    body: Option<web::Json<serde_json::Value>>,
) -> Result<HttpResponse, AppError> {
    // Get user ID from query parameters
    let user_id = if let Some(id) = &query.user_id {
        if !id.is_empty() {
            id.clone()
        } else {
            // Try to get from body
            if let Some(body_data) = &body {
                if let Some(id) = body_data.get("user_id").and_then(|v| v.as_str()) {
                    id.to_string()
                } else {
                    return Err(AppError::ValidationError("user_id is required".to_string()));
                }
            } else {
                return Err(AppError::ValidationError("user_id is required".to_string()));
            }
        }
    } else {
        // Try to get from body
        if let Some(body_data) = &body {
            if let Some(id) = body_data.get("user_id").and_then(|v| v.as_str()) {
                id.to_string()
            } else {
                return Err(AppError::ValidationError("user_id is required".to_string()));
            }
        } else {
            return Err(AppError::ValidationError("user_id is required".to_string()));
        }
    };

    // Rotate keys
    let keys = data.keys_service.rotate_keys(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Encryption keys rotated successfully. Store these keys securely as private keys will not be accessible again.".to_string(),
        data: Some(keys),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    // Set cache control headers to prevent caching of sensitive information
    Ok(HttpResponse::Ok()
        .append_header(("Cache-Control", "no-store"))
        .append_header(("Pragma", "no-cache"))
        .json(response))
}

// Revoke encryption keys for a user
pub async fn revoke_encryption_keys(
    data: web::Data<KeysControllerState>,
    query: web::Query<UserIdQuery>,
    body: Option<web::Json<serde_json::Value>>,
) -> Result<HttpResponse, AppError> {
    // Get user ID from query parameters
    let user_id = if let Some(id) = &query.user_id {
        if !id.is_empty() {
            id.clone()
        } else {
            // Try to get from body
            if let Some(body_data) = &body {
                if let Some(id) = body_data.get("user_id").and_then(|v| v.as_str()) {
                    id.to_string()
                } else {
                    return Err(AppError::ValidationError("user_id is required".to_string()));
                }
            } else {
                return Err(AppError::ValidationError("user_id is required".to_string()));
            }
        }
    } else {
        // Try to get from body
        if let Some(body_data) = &body {
            if let Some(id) = body_data.get("user_id").and_then(|v| v.as_str()) {
                id.to_string()
            } else {
                return Err(AppError::ValidationError("user_id is required".to_string()));
            }
        } else {
            return Err(AppError::ValidationError("user_id is required".to_string()));
        }
    };

    // Revoke keys
    data.keys_service.revoke_keys(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Encryption keys revoked successfully. All encrypted sessions have been terminated.".to_string(),
        data: Some(serde_json::json!({
            "revoked_at": Utc::now().timestamp()
        })),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Get pre-key bundle for a user
pub async fn get_pre_key_bundle(
    data: web::Data<KeysControllerState>,
    path: web::Path<String>,
) -> Result<HttpResponse, AppError> {
    let user_id = path.into_inner();

    // Retrieve public keys from database
    let bundle = data.keys_service.retrieve_public_keys(&user_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Pre-key bundle retrieved successfully.".to_string(),
        data: Some(bundle),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}

// Get session status with another user
pub async fn get_session_status(
    data: web::Data<KeysControllerState>,
    path: web::Path<String>,
    query: web::Query<UserIdQuery>,
    body: Option<web::Json<serde_json::Value>>,
) -> Result<HttpResponse, AppError> {
    // Get user ID from query parameters
    let user_id = if let Some(id) = &query.user_id {
        if !id.is_empty() {
            id.clone()
        } else {
            // Try to get from body
            if let Some(body_data) = &body {
                if let Some(id) = body_data.get("user_id").and_then(|v| v.as_str()) {
                    id.to_string()
                } else {
                    return Err(AppError::ValidationError("user_id is required".to_string()));
                }
            } else {
                return Err(AppError::ValidationError("user_id is required".to_string()));
            }
        }
    } else {
        // Try to get from body
        if let Some(body_data) = &body {
            if let Some(id) = body_data.get("user_id").and_then(|v| v.as_str()) {
                id.to_string()
            } else {
                return Err(AppError::ValidationError("user_id is required".to_string()));
            }
        } else {
            return Err(AppError::ValidationError("user_id is required".to_string()));
        }
    };

    // Get contact ID from path
    let contact_id = path.into_inner();

    // Retrieve session from database
    let session = data.keys_service.get_session_status(&user_id, &contact_id).await?;

    let response = ApiResponse {
        success: true,
        message: "Session status retrieved successfully.".to_string(),
        data: Some(serde_json::json!({
            "is_active": session.is_active,
            "last_updated": session.last_updated.timestamp(),
            "send_chain_length": session.send_chain_length,
            "receive_chain_length": session.receive_chain_length
        })),
        entities: None,
        timestamp: Some(Utc::now().timestamp() as u64),
        duration: None,
        count: None,
        cursor: None,
    };

    Ok(HttpResponse::Ok().json(response))
}


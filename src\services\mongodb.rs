use mongodb::{Client, Database, options::ClientOptions};
use std::env;
use log::{info, error};
use crate::errors::AppError;

pub struct MongoDBService {
    pub database: Database,
}

impl MongoDBService {
    pub async fn new() -> Result<Self, AppError> {

        let mongodb_uri = env::var("MONGODB_URI")
            .map_err(|_| AppError::ConfigError("MONGODB_URI environment variable is required".to_string()))?;

        let database_name = env::var("MONGODB_DATABASE")
            .map_err(|_| AppError::ConfigError("MONGODB_DATABASE environment variable is required".to_string()))?;

        info!("Connecting to MongoDB...");
        let client_options = ClientOptions::parse(&mongodb_uri)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to parse MongoDB connection string: {}", e)))?;
        let client = Client::with_options(client_options)
            .map_err(|e| AppError::DatabaseError(format!("Failed to create MongoDB client: {}", e)))?;

        let database = client.database(&database_name);

        match client.list_database_names(None, None).await {
            Ok(_) => info!("Successfully connected to MongoDB"),
            Err(e) => {
                error!("Failed to connect to MongoDB: {}", e);
                return Err(AppError::DatabaseError(format!("Failed to connect to MongoDB: {}", e)));
            }
        }

        Ok(Self { database })
    }

    pub fn collection<T>(&self, name: &str) -> mongodb::Collection<T> {
        self.database.collection(name)
    }
}

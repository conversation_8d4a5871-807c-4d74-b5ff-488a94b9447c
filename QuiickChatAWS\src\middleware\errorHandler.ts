import { Request, Response, NextFunction } from 'express';
import logger from '@/utils/logger';
import { ApiError, ApiResponse } from '@/types';

/**
 * Global error handling middleware
 */
export function errorHandler(
  error: Error | ApiError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // Log the error
  logger.error('API Error', {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // Determine status code
  const statusCode = (error as ApiError).statusCode || 500;
  const errorCode = (error as ApiError).code || 'INTERNAL_ERROR';

  // Create error response
  const response: ApiResponse = {
    success: false,
    message: error.message || 'Internal server error',
    error: error.message,
    code: errorCode,
    timestamp: new Date().toISOString(),
  };

  // Don't expose internal errors in production
  if (statusCode === 500 && process.env.NODE_ENV === 'production') {
    response.message = 'Internal server error';
    response.error = 'An unexpected error occurred';
  }

  res.status(statusCode).json(response);
}

/**
 * 404 Not Found handler
 */
export function notFoundHandler(req: Request, res: Response): void {
  logger.warn('404 Not Found', {
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  const response: ApiResponse = {
    success: false,
    message: 'Endpoint not found',
    error: `Cannot ${req.method} ${req.path}`,
    code: 'NOT_FOUND',
    timestamp: new Date().toISOString(),
    data: {
      availableEndpoints: [
        'GET /health',
        'GET /api/v1/status',
        'POST /api/v1/start',
        'POST /api/v1/stop',
        'POST /api/v1/restart',
      ],
    },
  };

  res.status(404).json(response);
}

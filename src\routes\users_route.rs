use actix_web::web;
use crate::controllers::users_controller;

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/users")
            // Public routes (no authentication required)
            .route("/init", web::get().to(users_controller::init_user))
            .route("/debug-phone", web::get().to(users_controller::debug_phone_lookup))
            .route("/debug-twilio", web::get().to(users_controller::debug_twilio_test))

            // Protected routes (authentication required)
            .route("/me", web::get().to(users_controller::get_current_user))  // Main user endpoint
            .route("/profile", web::put().to(users_controller::update_profile))   // UPDATE profile only
            .route("/upload-profile-picture", web::post().to(users_controller::upload_profile_picture))
            .route("/profile/picture/{user_id}", web::get().to(users_controller::get_profile_picture))
            .route("/delete-account", web::delete().to(users_controller::delete_user_account))

            // Settings routes
            .route("/settings", web::get().to(users_controller::get_settings))
            .route("/settings/notifications", web::put().to(users_controller::update_notification_settings))
            .route("/settings/privacy", web::put().to(users_controller::update_privacy_settings))
            .route("/settings/appearance", web::put().to(users_controller::update_appearance_settings))
            .route("/settings/language", web::put().to(users_controller::update_language))
        );
}
